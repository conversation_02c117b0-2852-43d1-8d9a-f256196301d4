import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js@2.39.8";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface EmailData {
  from: string;
  to: string;
  subject: string;
  html: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('Starting event notification process...');

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get application ID from request
    const { applicationId } = await req.json();
    console.log('Application ID:', applicationId);
    
    if (!applicationId) {
      throw new Error('Application ID is required');
    }

    // Get application and event details
    const { data: application, error: applicationError } = await supabaseClient
      .from('event_applications')
      .select(`
        *,
        events (
          title,
          date,
          start_time,
          end_time,
          address,
          description
        )
      `)
      .eq('id', applicationId)
      .single();

    if (applicationError) {
      console.error('Error fetching application:', applicationError);
      throw applicationError;
    }
    if (!application) {
      console.error('Application not found');
      throw new Error('Application not found');
    }

    console.log('Application data:', application);

    // Generate QR code URL
    const qrCodeUrl = `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(application.qr_code)}&dpi=96&quietzone=5`;

    // Create email content
    const emailData: EmailData = {
      from: '商聯思維 Synerthink <<EMAIL>>',
      to: application.email,
      subject: `活動報名確認：${application.events.title}`,
      html: `
        <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6B4593;">活動報名確認</h1>
          
          <p>親愛的 ${application.full_name}：</p>
          
          <p>感謝您報名參加以下活動：</p>
          
          <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="color: #333; margin-top: 0;">${application.events.title}</h2>
            
            <p><strong>日期：</strong>${new Date(application.events.date).toLocaleDateString('zh-HK')}</p>
            <p><strong>時間：</strong>${application.events.start_time} - ${application.events.end_time}</p>
            <p><strong>地點：</strong>${application.events.address}</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="margin-bottom: 15px;"><strong>您的入場二維碼</strong></p>
            <img src="${qrCodeUrl}" alt="QR Code" style="width: 200px; height: 200px;">
            <p style="color: #666; font-size: 0.9em;">請於活動當天出示此二維碼以供登記出席</p>
          </div>

          <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0;"><strong>重要提醒：</strong></p>
            <ul style="margin: 10px 0;">
              <li>請準時到達活動場地</li>
              <li>記得帶備入場二維碼</li>
              <li>如有任何查詢，請回覆此電郵</li>
            </ul>
          </div>

          <p style="color: #666; font-size: 0.9em; text-align: center; margin-top: 30px;">
            此郵件由系統自動發送，請勿回覆。
          </p>
        </div>
      `,
    };

    console.log('Sending email to:', emailData.to);

    // Send email using Resend
    const resendResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('RESEND_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    });

    if (!resendResponse.ok) {
      const errorData = await resendResponse.json();
      console.error('Resend API error:', errorData);
      throw new Error(`Failed to send email: ${JSON.stringify(errorData)}`);
    }

    console.log('Email sent successfully');

    return new Response(
      JSON.stringify({ success: true }),
      { 
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Function error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
  }
});