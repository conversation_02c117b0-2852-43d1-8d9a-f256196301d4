<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>個人資料</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <ion-header collapse="condense">
          <ion-toolbar>
            <ion-title size="large">個人資料</ion-title>
          </ion-toolbar>
        </ion-header>

        <div class="ion-padding">
          <ion-list v-if="currentUser">
            <!-- Profile Picture Section -->
            <ion-item-group class="profile-picture-section">
              <div class="profile-header">
                <div class="avatar-container" @click="showProfilePictureModal = true">
                  <ion-avatar class="profile-avatar">
                    <img :src="currentUser.avatar || 'https://ionicframework.com/docs/img/demos/avatar.svg'" :alt="currentUser.full_name">
                  </ion-avatar>
                  <div class="avatar-edit-overlay">
                    <ion-icon :icon="cameraOutline"></ion-icon>
                  </div>
                </div>
                <div class="profile-info">
                  <h2>{{ currentUser.full_name }}</h2>
                  <p>{{ getRoleLabel(currentUser.role) }}</p>
                </div>
              </div>
            </ion-item-group>

            <!-- Referral Code Section -->
            <ion-item-group class="referral-section">
              <ion-item-divider>
                <ion-label>推薦碼</ion-label>
              </ion-item-divider>

              <div class="referral-content">
                <div class="qr-code-container">
                  <img :src="qrCodeUrl" alt="Referral QR Code" class="qr-code" />
                  <p class="code-label">{{ currentUser.referral_code }}</p>
                </div>

                <div class="referral-actions">
                  <ion-button expand="block" fill="outline" @click="copyReferralLink">
                    <ion-icon :icon="linkOutline" slot="start"></ion-icon>
                    複製推薦連結
                  </ion-button>
                  <ion-button expand="block" fill="outline" @click="shareReferralLink">
                    <ion-icon :icon="shareOutline" slot="start"></ion-icon>
                    分享推薦碼
                  </ion-button>
                </div>

                <div class="referral-network">
                  <ion-button expand="block" color="primary" router-link="/referral-tree">
                    <ion-icon :icon="peopleCircleOutline" slot="start"></ion-icon>
                    查看我的推薦網絡
                  </ion-button>
                </div>

                <div class="referral-info">
                  <p>分享您的推薦碼給朋友，邀請他們加入！</p>
                  <small>您的用戶名稱是：<b>{{ currentUser?.username }}</b> ({{ currentUser.full_name }})</small><br />
                </div>
              </div>
            </ion-item-group>

            <!-- My Shop Section -->
            <ion-item-group>
              <ion-item-divider>
                <ion-label>我的商店</ion-label>
              </ion-item-divider>
              <template v-if="userShop">
                <ion-item button @click="navigateToShop(userShop.id)" detail>
                  <ion-thumbnail slot="start" v-if="userShop.logo">
                    <img :src="userShop.logo" :alt="userShop.name">
                  </ion-thumbnail>
                  <ion-label>
                    <h2>{{ userShop.name }}</h2>
                  </ion-label>
                </ion-item>
              </template>
              <ion-item v-else lines="none">
                <ion-label>
                  <p class="ion-text-center">您還沒有商店</p>
                  <div class="ion-text-center ion-margin-top">
                    <ion-button @click="showCreateShopModal = true">
                      創建商店
                      <ion-icon :icon="storefront" slot="end"></ion-icon>
                    </ion-button>
                  </div>
                </ion-label>
              </ion-item>
              <!--
              <ion-item v-if="userShop && currentUser?.is_admin" lines="none" button
                        @click="showCreateShopModal = true; isAdminCreating = true">
                <ion-label>
                  新增商店
                </ion-label>
                <ion-icon :icon="add" slot="end"></ion-icon>
              </ion-item>
            -->
            </ion-item-group>

            <!-- My Branch Section (for 分會長) -->
            <ion-item-group v-if="isPresident">
              <ion-item-divider>
                <ion-label>我創立的分會</ion-label>
              </ion-item-divider>
              <template v-if="userBranch">
                <ion-item button @click="navigateToBranch(userBranch.id)" detail>
                  <ion-thumbnail slot="start" v-if="userBranch.logo">
                    <img :src="userBranch.logo" :alt="userBranch.name">
                  </ion-thumbnail>
                  <ion-label>
                    <h2>{{ userBranch.name }}</h2>
                  </ion-label>
                </ion-item>
              </template>
              <ion-item v-else lines="none">
                <ion-label>
                  <p class="ion-text-center">您還沒有分會</p>
                  <div class="ion-text-center ion-margin-top">
                    <ion-button @click="showCreateBranchModal = true">
                      創建分會
                      <ion-icon :icon="peopleCircleOutline" slot="end"></ion-icon>
                    </ion-button>
                  </div>
                </ion-label>
              </ion-item>
              <!--
              <ion-item v-if="userBranch && currentUser?.is_admin" lines="none" button
                        @click="showCreateBranchModal = true; isAdminCreating = true">
                <ion-label>新增分會</ion-label>
                <ion-icon :icon="add" slot="end"></ion-icon>
              </ion-item>
              -->
            </ion-item-group>

            <!-- My Branch Memberships Section -->
            <ion-item-group>
              <ion-item-divider>
                <ion-label>我已加入的分會</ion-label>
              </ion-item-divider>
              <template v-if="branchMemberships.length > 0 || pendingApplications.length > 0">
                <!-- Active Memberships -->
                <ion-item v-for="membership in branchMemberships" :key="membership.id" button @click="navigateToBranch(membership.branch_id)">
                  <ion-thumbnail slot="start" v-if="membership.branches?.logo">
                    <img :src="membership.branches?.logo" :alt="membership.branches?.name">
                  </ion-thumbnail>
                  <ion-thumbnail slot="start" v-else>
                    <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c" alt="Default branch logo">
                  </ion-thumbnail>
                  <ion-label>
                    <h2>{{ membership.branches?.name }}</h2>
                    <p>{{ formatDate(membership.joined_at) }} 加入</p>
                  </ion-label>
                  <ion-icon :icon="chevronForward" slot="end"></ion-icon>
                </ion-item>

                <!-- Pending Applications (now shown as part of joined branches with status) -->
                <ion-item v-for="application in pendingApplications" :key="application.id" button @click="navigateToBranch(application.branch_id)">
                  <ion-thumbnail slot="start" v-if="application.branches?.logo">
                    <img :src="application.branches?.logo" :alt="application.branches?.name">
                  </ion-thumbnail>
                  <ion-thumbnail slot="start" v-else>
                    <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c" alt="Default branch logo">
                  </ion-thumbnail>
                  <ion-label>
                    <h2>{{ application.branches?.name }}</h2>
                    <p>{{ formatDate(application.created_at) }} 申請</p>
                    <ion-badge color="warning">審核中</ion-badge>
                  </ion-label>
                  <ion-icon :icon="chevronForward" slot="end"></ion-icon>
                </ion-item>
              </template>

              <ion-item v-if="branchMemberships.length === 0 && pendingApplications.length === 0" lines="none">
                <ion-label>
                  <p class="ion-text-center">您還沒有加入任何分會</p>
                  <div class="ion-text-center ion-margin-top">
                    <ion-button router-link="/branches">
                      瀏覽分會
                      <ion-icon :icon="peopleCircleOutline" slot="end"></ion-icon>
                    </ion-button>
                  </div>
                </ion-label>
              </ion-item>
            </ion-item-group>

            <!-- My Organizations Section -->
            <ion-item-group v-if="isPresident && userBranch">
              <!-- Created Organizations -->
              <template v-if="userOrganizations.length > 0">
                <ion-item-divider>
                  <ion-label>我創立的組織</ion-label>
                </ion-item-divider>
                <ion-item v-for="organization in userOrganizations" :key="organization.id" button @click="navigateToOrganization(organization.id)" detail>
                  <ion-thumbnail slot="start">
                    <img :src="organization.logo || 'https://images.unsplash.com/photo-1549923746-c502d488b3ea'" :alt="organization.name">
                  </ion-thumbnail>
                  <ion-label>
                    <h2>{{ organization.name }}</h2>
                    <ion-badge color="primary">創辦人</ion-badge>
                  </ion-label>
                </ion-item>
              </template>

              <!-- Joined Organizations -->
              <template v-if="joinedOrganizations.length > 0">
                <ion-item-divider>
                  <ion-label>我已加入的組織</ion-label>
                </ion-item-divider>
                <ion-item v-for="membership in joinedOrganizations" :key="membership.organizations.id" button @click="navigateToOrganization(membership.organizations.id)" detail>
                  <ion-thumbnail slot="start" v-if="membership.organizations.logo">
                    <img :src="membership.organizations.logo" :alt="membership.organizations.name">
                  </ion-thumbnail>
                  <ion-thumbnail slot="start" v-else>
                    <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea" alt="Default organization logo">
                  </ion-thumbnail>
                  <ion-label>
                    <h2>{{ membership.organizations.name }}</h2>
                    <p>{{ formatDate(membership.joined_at) }} 加入</p>
                  </ion-label>
                </ion-item>
              </template>
            </ion-item-group>

            <!-- My Events Section -->
            <ion-item-group>
              <ion-item-divider>
                <ion-label>我的活動</ion-label>
              </ion-item-divider>
              <ion-item button router-link="/events?tab=registered">
                <ion-icon :icon="calendarOutline" slot="start" color="primary"></ion-icon>
                <ion-label>
                  <h2>已報名活動</h2>
                  <p>查看活動詳情及入場二維碼</p>
                </ion-label>
                <ion-icon :icon="chevronForward" slot="end"></ion-icon>
              </ion-item>
              <ion-item button router-link="/liked-events">
                <ion-icon :icon="heart" slot="start" color="danger"></ion-icon>
                <ion-label>
                  <h2>已收藏活動</h2>
                  <p>查看您收藏的活動</p>
                </ion-label>
                <ion-icon :icon="chevronForward" slot="end"></ion-icon>
              </ion-item>
            </ion-item-group>

            <!-- My Connections Section -->
            <ion-item-group>
              <ion-item-divider>
                <ion-label>我的人脈</ion-label>
              </ion-item-divider>
              <ion-item button router-link="/liked-users">
                <ion-icon :icon="peopleOutline" slot="start" color="tertiary"></ion-icon>
                <ion-label>
                  <h2>已收藏用戶</h2>
                  <p>查看您收藏的用戶及備註</p>
                </ion-label>
                <ion-icon :icon="chevronForward" slot="end"></ion-icon>
              </ion-item>
            </ion-item-group>

            <!-- Bonus Section -->
            <ion-item-group>
              <ion-item-divider>
                <ion-label>奬金</ion-label>
              </ion-item-divider>
              <ion-item button router-link="/bonus">
                <ion-icon :icon="wallet" slot="start" color="success"></ion-icon>
                <ion-label>
                  <h2>奬金</h2>
                  <p>查看您的奬金記錄與餘額</p>
                </ion-label>
                <ion-icon :icon="chevronForward" slot="end"></ion-icon>
              </ion-item>
            </ion-item-group>

            <!-- TODO: Checklist
             升級為商家會員
             付款有待驗證?
             補充資料 (bank account)
             Accept T&C (簽協議書)
             Payment confirmed
             Create Shop
             升級為分會長
             創建分會 (升級)
             創建活動?
            -->

            <!-- User Profile Information -->
            <ion-item-group>
              <ion-item-divider>
                <ion-label>基本資料</ion-label>
              </ion-item-divider>
              <ion-item>
                <ion-label>
                  <h3>會員類型</h3>
                  <p>{{ getRoleLabel(currentUser?.role) }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>加入日期</h3>
                  <p>{{ formatDate(currentUser?.created_at) }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>推薦人</h3>
                  <p>{{ currentUser?.referrer_id ? (referrerName || '未知') : '公司' }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>用戶名稱</h3>
                  <p>{{ currentUser?.username }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>姓名</h3>
                  <p>{{ currentUser?.full_name }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>電郵地址</h3>
                  <p>{{ currentUser?.email }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>電話號碼</h3>
                  <p>{{ currentUser?.phone }}</p>
                </ion-label>
              </ion-item>
            </ion-item-group>

            <ion-item-group v-if="isMerchantOrPresident">
              <ion-item-divider>
                <ion-label>商家資料</ion-label>
              </ion-item-divider>
              <ion-item>
                <ion-label>
                  <h3>行業</h3>
                  <p>{{ currentUser?.industry }}</p>
                </ion-label>
              </ion-item>
              <ion-item>
                <ion-label>
                  <h3>公司名稱</h3>
                  <p>{{ currentUser?.company_name }}</p>
                </ion-label>
              </ion-item>
            </ion-item-group>
          </ion-list>

          <div class="ion-padding">
            <!-- TODO: Add Upgrade Button -->
            <!--<ion-button expand="block" color="primary" @click="handleUpgrade" v-if="canUpgrade">
              升級會員
            </ion-button>-->
            <ion-button expand="block" color="secondary" @click="showEditProfileModal = true" class="edit-profile-button">
              <ion-icon slot="start" :icon="createOutline"></ion-icon>
              編輯個人資料
            </ion-button>
            <!--<ion-button expand="block" color="primary" @click="showChangePasswordModal = true" class="change-password-button">
              <ion-icon slot="start" :icon="lockClosedOutline"></ion-icon>
              更改密碼
            </ion-button>-->
            <ion-button expand="block" color="medium" @click="showLogoutConfirm" class="logout-button">
              <ion-icon slot="start" :icon="logOutOutline"></ion-icon>
              登出
            </ion-button>
            <div class="ion-text-center">
              <ion-button fill="clear" size="small" color="danger" @click="showDeleteAccountConfirm" class="delete-account-button" :disabled="isDeleting">
                <ion-spinner v-if="isDeleting" name="dots" slot="start"></ion-spinner>
                <ion-icon v-else slot="start" :icon="trashOutline"></ion-icon>
                {{ isDeleting ? '處理中...' : '刪除帳戶' }}
              </ion-button>
            </div>
          </div>
        </div>

        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>

        <ion-alert
          :is-open="showLogoutAlert"
          header="確認登出"
          message="確定要登出嗎？"
          :buttons="[
            {
              text: '取消',
              role: 'cancel',
              handler: () => {
                showLogoutAlert = false;
              },
            },
            {
              text: '確定',
              handler: () => {
                handleLogout();
                showLogoutAlert = false;
              },
            },
          ]"
        ></ion-alert>

        <ion-alert
          :is-open="showDeleteAccountAlert"
          header="確認刪除帳戶"
          message="警告：此操作無法撤銷。刪除帳戶後，您將無法再使用此帳戶登入。確定要刪除您的帳戶嗎？"
          :buttons="[
            {
              text: '取消',
              role: 'cancel',
              handler: () => {
                showDeleteAccountAlert = false;
              },
            },
            {
              text: '確定刪除',
              role: 'destructive',
              handler: () => {
                handleDeleteAccount();
              },
            },
          ]"
        ></ion-alert>

        <ShopFormModal
          :is-open="showCreateShopModal"
          :shop="isAdminCreating ? null : userShop"
          @close="showCreateShopModal = false; isAdminCreating = false;"
          @created="handleShopCreated"
          @updated="handleShopUpdated"
        />

        <BranchFormModal
          :is-open="showCreateBranchModal"
          :branch="isAdminCreating ? null : userBranch"
          @close="showCreateBranchModal = false; isAdminCreating = false"
          @created="handleBranchCreated"
          @updated="handleBranchUpdated"
        />

        <OrganizationFormModal
          :is-open="showCreateOrganizationModal"
          @close="showCreateOrganizationModal = false"
          @created="handleOrganizationCreated"
          @updated="handleOrganizationUpdated"
        />

        <ProfilePictureModal
          :is-open="showProfilePictureModal"
          :current-avatar="currentUser?.avatar"
          @close="showProfilePictureModal = false"
          @updated="handleAvatarUpdated"
        />

        <EditProfileModal
          :is-open="showEditProfileModal"
          :current-user="currentUser"
          @close="showEditProfileModal = false"
          @updated="handleProfileUpdated"
        />

        <ChangePasswordModal
          :is-open="showChangePasswordModal"
          @close="showChangePasswordModal = false"
          @updated="handlePasswordUpdated"
        />
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonAvatar,
  IonItemGroup,
  IonItemDivider,
  IonItem,
  IonLabel,
  IonButton,
  IonIcon,
  IonToast,
  IonAlert,
  IonThumbnail,
  IonSpinner,
  IonBadge,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  logOutOutline,
  chevronForward,
  storefront,
  linkOutline,
  shareOutline,
  calendarOutline,
  wallet,
  peopleCircleOutline,
  peopleOutline,
  trashOutline,
  heart,
  cameraOutline,
  createOutline,
  lockClosedOutline,
  add,
  businessOutline,
} from 'ionicons/icons';
import { useUserStore } from '@/stores/user';
import { useAuthStore } from '@/stores/auth';
import { useOrganizationStore } from '@/stores/organization';
import { supabase } from '@/lib/supabase';
import type { UserRole } from '@/types';
import ShopFormModal from '@/components/ShopFormModal.vue';
import BranchFormModal from '@/components/BranchFormModal.vue';
import OrganizationFormModal from '@/components/OrganizationFormModal.vue';
import ProfilePictureModal from '@/components/ProfilePictureModal.vue';
import EditProfileModal from '@/components/EditProfileModal.vue';
import ChangePasswordModal from '@/components/ChangePasswordModal.vue';
import { Share } from '@capacitor/share';
import { Clipboard } from '@capacitor/clipboard';
import { format } from 'date-fns';

const router = useRouter();
const userStore = useUserStore();
const authStore = useAuthStore();
const organizationStore = useOrganizationStore();
const currentUser = computed(() => userStore.currentUser);
const userShop = computed(() => userStore.userShop);
const userBranch = computed(() => userStore.userBranch);
const referrer = computed(() => userStore.referrer);
const pendingApplications = computed(() => userStore.pendingBranchApplications);
const branchMemberships = computed(() => userStore.branchMemberships);
const userOrganizations = computed(() => organizationStore.userOrganizations);
const joinedOrganizations = computed(() => organizationStore.joinedOrganizations);
const toastMessage = ref('');
const showLogoutAlert = ref(false);
const showDeleteAccountAlert = ref(false);
const isDeleting = ref(false);
const isAdminCreating = ref(false);
const showCreateShopModal = ref(false);
const showCreateBranchModal = ref(false);
const showCreateOrganizationModal = ref(false);
const showProfilePictureModal = ref(false);
const showEditProfileModal = ref(false);
const showChangePasswordModal = ref(false);

// For backward compatibility with existing code
const referrerName = computed(() => referrer.value?.full_name || null);

const isMerchantOrPresident = computed(() => {
  return currentUser.value?.role === 'merchant' || currentUser.value?.role === 'president';
});

const isPresident = computed(() => {
  return currentUser.value?.role === 'president';
});

// Uncomment when upgrade functionality is implemented
// const canUpgrade = computed(() => {
//   return currentUser.value?.role === 'free' || currentUser.value?.role === 'merchant';
// });

// Generate referral link and QR code
const referralLink = computed(() => {
  if (!currentUser.value?.referral_code) return '';
  //const baseUrl = window.location.origin;
  const baseUrl = "https://syner-biz.com";
  return `${baseUrl}/register?code=${currentUser.value.referral_code}`;
});

const qrCodeUrl = computed(() => {
  return `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(referralLink.value)}&dpi=96&quietzone=5`;
});

const formatDate = (dateString: string) => {
  if (!dateString) return '';
  try {
    return format(new Date(dateString), 'yyyy/MM/dd');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

// Refresh data when the view is entered
onIonViewDidEnter(async () => {
  if (authStore.currentUser?.id) {
    // Refresh user data to ensure we have the latest shop and branch info
    await authStore.refreshUserData();

    // Load organization data for presidents
    if (isPresident.value) {
      await organizationStore.fetchUserOrganizations();
      await organizationStore.fetchJoinedOrganizations();
    }
  }
});

const getRoleLabel = (role?: UserRole) => {
  switch (role) {
    case 'free':
      return '普通免費會員';
    case 'merchant':
      return '商家會員';
    case 'president':
      return '分會長';
    default:
      return '未知';
  }
};

const copyReferralLink = async () => {
  try {
    await Clipboard.write({ string: referralLink.value });
    toastMessage.value = '推薦連結已複製';
  } catch (error) {
    try {
      await navigator.clipboard.writeText(referralLink.value);
      toastMessage.value = '推薦連結已複製';
    } catch (e) {
      console.error('Failed to copy:', error);
      toastMessage.value = '複製失敗，請手動複製';
    }
  }
};

const shareReferralLink = async () => {
  try {
    await Share.share({
      title: '加入商聯思維 Synerthink',
      text: `使用我的推薦碼 ${currentUser.value?.referral_code} 註冊商聯思維 Synerthink！`,
      url: referralLink.value,
      dialogTitle: '分享商聯思維 Synerthink',
    });
  } catch (error) {
    if (navigator.share) {
      try {
        await navigator.share({
          title: '加入商聯思維 Synerthink',
          text: `使用我的推薦碼 ${currentUser.value?.referral_code} 註冊商聯思維 Synerthink！
  ${referralLink.value}`,
          //url: referralLink.value,
        });
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error);
          toastMessage.value = '分享失敗';
        }
      }
    } else {
      await copyReferralLink();
    }
  }
};

// Uncomment when upgrade functionality is implemented
// const handleUpgrade = () => {
//   // TODO: Implement upgrade logic
//   console.log('Upgrade membership');
// };

const showLogoutConfirm = () => {
  showLogoutAlert.value = true;
};

const showDeleteAccountConfirm = () => {
  showDeleteAccountAlert.value = true;
};

const handleDeleteAccount = async () => {
  try {
    isDeleting.value = true;

    // Generate random strings for both email and password
    const randomString = Math.random().toString(36).slice(-20) +
                        Math.random().toString(36).slice(-20);
    const randomPassword = Math.random().toString(36).slice(-20) +
                          Math.random().toString(36).slice(-20);

    // Get the current user's email
    const currentEmail = authStore.currentUser?.email;
    if (!currentEmail) {
      throw new Error('User email not found');
    }

    // Create a new email that the user won't know
    // Format: <EMAIL>
    const emailParts = currentEmail.split('@');
    const newEmail = `${randomString}.deleted.${emailParts[0]}@${emailParts[1]}`;

    // Generate a random username for the deleted account
    const randomUsername = `deleted_${randomString.substring(0, 10)}`;

    // Update email, password, and user metadata
    const { error } = await supabase.auth.updateUser({
      email: newEmail,
      password: randomPassword,
      data: {
        username: randomUsername
      }
    });

    if (error) {
      throw error;
    }

    // Also update the email and username in the users table to maintain consistency
    const { error: updateError } = await supabase
      .from('users')
      .update({
        email: newEmail,
        username: randomUsername
      })
      .eq('id', authStore.currentUser?.id as string);

    if (updateError) {
      console.error('Error updating user data in database:', updateError);
      // Continue anyway since the auth record is already updated
    }

    // Sign the user out
    await authStore.signOut();

    toastMessage.value = '帳戶已成功刪除';
    router.push('/home');
  } catch (error) {
    console.error('Error deleting account:', error);
    toastMessage.value = '刪除帳戶時發生錯誤，請稍後再試';
  } finally {
    isDeleting.value = false;
    showDeleteAccountAlert.value = false;
  }
};

const handleLogout = async () => {
  try {
    // The userStore.logout() will be called by authStore.signOut()
    await authStore.signOut();
    router.push('/home');
  } catch (error) {
    console.error('Logout error:', error);
    toastMessage.value = '登出時發生錯誤';
  }
};

const navigateToShop = (shopId: string) => {
  router.push(`/shops/${shopId}`);
};

const navigateToBranch = (branchId: string) => {
  router.push(`/branches/${branchId}`);
};

const navigateToOrganization = (organizationId: string) => {
  router.push(`/organizations/${organizationId}`);
};

const handleShopCreated = async () => {
  // Refresh the user data to get the updated shop
  await authStore.refreshUserData();
  showCreateShopModal.value = false;
  toastMessage.value = '商店創建成功！';
};

const handleShopUpdated = async () => {
  // Refresh the user data to get the updated shop
  await authStore.refreshUserData();
  showCreateShopModal.value = false;
  toastMessage.value = '商店更新成功！';
};

const handleBranchCreated = async () => {
  // Refresh the user data to get the updated branch
  await authStore.refreshUserData();
  showCreateBranchModal.value = false;
  toastMessage.value = '分會創建成功！';
};

const handleBranchUpdated = async () => {
  // Refresh the user data to get the updated branch
  await authStore.refreshUserData();
  showCreateBranchModal.value = false;
  toastMessage.value = '分會更新成功！';
};

const handleOrganizationCreated = async () => {
  // Refresh organization data
  await organizationStore.fetchUserOrganizations();
  showCreateOrganizationModal.value = false;
  toastMessage.value = '組織創建成功！';
};

const handleOrganizationUpdated = async () => {
  // Refresh organization data
  await organizationStore.fetchUserOrganizations();
  showCreateOrganizationModal.value = false;
  toastMessage.value = '組織更新成功！';
};

const handleAvatarUpdated = async () => {
  // Avatar has already been updated in the database and user store refreshed by the modal
  showProfilePictureModal.value = false;
  toastMessage.value = '頭像已更新';
};

const handleProfileUpdated = async () => {
  // Profile has already been updated in the database and user store refreshed by the modal
  showEditProfileModal.value = false;
  toastMessage.value = '個人資料已更新';
};

const handlePasswordUpdated = async () => {
  // Password has already been updated
  showChangePasswordModal.value = false;
  toastMessage.value = '密碼已更改';
};
</script>

<style scoped>
.edit-profile-button {
  margin-top: 1rem;
}

.change-password-button {
  margin-top: 1rem;
}

.logout-button {
  margin-top: 1rem;
}

.delete-account-button {
  margin-top: 2rem;
  font-size: 0.7rem;
}

ion-thumbnail {
  --size: 60px;
  margin-right: 1rem;
}

ion-thumbnail img {
  border-radius: 8px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Profile Picture Section */
.profile-picture-section {
  margin-bottom: 1.5rem;
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.avatar-container {
  position: relative;
  margin-right: 1.5rem;
  cursor: pointer;
}

.profile-avatar {
  --size: 80px;
  width: var(--size);
  height: var(--size);
  border: 2px solid var(--ion-color-light);
}

.avatar-edit-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background: var(--ion-color-primary);
  color: white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.avatar-edit-overlay ion-icon {
  font-size: 16px;
}

.profile-info {
  flex: 1;
}

.profile-info h2 {
  margin: 0 0 0.25rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.profile-info p {
  margin: 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.referral-section {
  margin-bottom: 1rem;
}

.referral-content {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
}

.qr-code-container {
  margin-bottom: 1.5rem;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin: 0 auto 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.code-label {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--ion-color-primary);
  margin: 0;
  letter-spacing: 2px;
}

.referral-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.referral-actions ion-button {
  flex: 1;
}

.referral-network {
  margin-bottom: 1rem;
}

.referral-info {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

.pending-divider {
  margin-top: 1rem;
  --background: var(--ion-color-light);
  --color: var(--ion-color-medium);
  font-size: 0.9rem;
}

ion-badge {
  margin-top: 0.5rem;
}

.sub-divider {
  --background: var(--ion-color-light-shade);
  --color: var(--ion-color-medium-shade);
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .referral-actions {
    flex-direction: column;
  }

  .qr-code {
    width: 180px;
    height: 180px;
  }

  .code-label {
    font-size: 1.25rem;
  }
}
</style>