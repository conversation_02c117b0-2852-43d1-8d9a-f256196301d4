<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/home" text="返回"></ion-back-button>
        </ion-buttons>
        <ion-title>用戶資料</ion-title>
        <ion-buttons slot="end" v-if="authStore.isAuthenticated && userProfile && userProfile.id !== authStore.currentUser?.id">
          <ion-button @click="toggleLikeUser">
            <ion-icon :icon="isUserLiked ? heart : heartOutline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <div v-if="isLoading" class="loading-container">
        <ion-spinner></ion-spinner>
        <p>載入中...</p>
      </div>

      <div v-else-if="userProfile" class="profile-container">
        <!-- User Basic Info -->
        <div class="user-header">
          <ion-avatar class="user-avatar">
            <img :src="userProfile.avatar || 'https://ionicframework.com/docs/img/demos/avatar.svg'" :alt="userProfile.full_name">
          </ion-avatar>
          <div class="user-info">
            <h1>{{ userProfile.full_name }}</h1>
            <div class="user-meta">
              <ion-badge color="primary">{{ getRoleLabel(userProfile.role) }}</ion-badge>
              <span class="industry" v-if="userProfile.industry">{{ userProfile.industry }}</span>
              <span class="company" v-if="userProfile.company_name">{{ userProfile.company_name }}</span>
            </div>
            <p class="join-date">{{ formatDate(userProfile.created_at) }} 加入</p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons" v-if="userProfile.id !== authStore.currentUser?.id">
          <ion-button expand="block" @click="showMessageModal = true">
            <ion-icon :icon="chatbubbleOutline" slot="start"></ion-icon>
            發送訊息
          </ion-button>
          <ion-button expand="block" color="secondary" @click="showLikeModal = true" :fill="isUserLiked ? 'solid' : 'outline'">
            <ion-icon :icon="isUserLiked ? heart : heartOutline" slot="start"></ion-icon>
            {{ isUserLiked ? '已收藏 - 編輯備註' : '收藏用戶' }}
          </ion-button>
        </div>

        <!-- User Content Tabs -->
        <ion-segment v-model="activeTab" mode="md" scrollable>
          <ion-segment-button value="info">
            <ion-label>基本資料</ion-label>
          </ion-segment-button>
          <!--<ion-segment-button value="shop" v-if="userShop">-->
          <ion-segment-button value="shop">
            <ion-label>商店</ion-label>
          </ion-segment-button>
          <!--<ion-segment-button value="branch" v-if="userBranch">-->
          <ion-segment-button value="branch">
            <ion-label>分會</ion-label>
          </ion-segment-button>
          <!--<ion-segment-button value="events" v-if="hostedEvents.length > 0">-->
          <ion-segment-button value="events">
            <ion-label>活動</ion-label>
          </ion-segment-button>
          <ion-segment-button value="interactions">
            <ion-label>互動記錄</ion-label>
          </ion-segment-button>
        </ion-segment>

        <!-- Info Tab -->
        <div v-if="activeTab === 'info'" class="tab-content">
          <ion-list>
            <ion-item>
              <ion-label>
                <h3>會員類型</h3>
                <p>{{ getRoleLabel(userProfile.role) }}</p>
              </ion-label>
            </ion-item>
            <ion-item v-if="userProfile.industry">
              <ion-label>
                <h3>行業</h3>
                <p>{{ userProfile.industry }}</p>
              </ion-label>
            </ion-item>
            <ion-item v-if="userProfile.company_name">
              <ion-label>
                <h3>公司名稱</h3>
                <p>{{ userProfile.company_name }}</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>加入日期</h3>
                <p>{{ formatDate(userProfile.created_at) }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- Shop Tab -->
        <div v-if="activeTab === 'shop' && userShop" class="tab-content">
          <ShopCard :shop="userShop" />
        </div>

        <!-- Branch Tab -->
        <div v-if="activeTab === 'branch' && userBranch" class="tab-content">
          <BranchCard :branch="userBranch" />

          <div v-for="ms in userProfile.branchMemberships" :key="ms.id" v-show="ms.branches.id != userBranch.id">
            <BranchCard :branch="ms.branches" />
          </div>
        </div>

        <!-- Events Tab -->
        <div v-if="activeTab === 'events'" class="tab-content">
          <div class="section">
            <h2>主辦的活動</h2>
            <div v-if="hostedEvents.length > 0" class="events-grid">
              <EventCard v-for="event in hostedEvents" :key="event.id" :event="event" />
            </div>
            <p v-else class="empty-text">暫無主辦的活動</p>
          </div>
          <div class="section">
            <h2>參與的活動</h2>
            <div v-if="userProfile.eventApplications?.length > 0" class="events-grid">
              <div v-for="ea in userProfile.eventApplications" :key="ea.id">
                <EventCard :event="ea.events" />
              </div>
            </div>
            <p v-else class="empty-text">暫無參與的活動</p>
          </div>
        </div>

        <!-- Interactions Tab -->
        <div v-if="activeTab === 'interactions'" class="tab-content">
          <div class="section">
            <h2>互動記錄</h2>
            <div v-if="interactions.length > 0" class="timeline">
              <div v-for="(interaction, index) in interactions" :key="interaction.id" class="timeline-item">
                <div class="timeline-icon" :class="getInteractionColor(interaction.type)">
                  <ion-icon :icon="getInteractionIcon(interaction.type)"></ion-icon>
                </div>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <h3 v-html="getInteractionTitle(interaction)"></h3>
                    <span class="timeline-date">{{ formatDateTime(interaction.created_at) }}</span>
                  </div>
                  <div v-if="interaction.notes" class="timeline-notes">
                    <p>{{ interaction.notes }}</p>
                  </div>
                </div>
              </div>
            </div>
            <p v-else class="empty-text">暫無互動記錄</p>
          </div>
        </div>
      </div>

      <div v-else class="error-container">
        <ion-icon :icon="alertCircleOutline"></ion-icon>
        <p>找不到用戶資料</p>
        <ion-button router-link="/tabs/home" fill="outline">
          返回首頁
        </ion-button>
      </div>

      <!-- Like User Modal -->
      <ion-modal :is-open="showLikeModal" @didDismiss="showLikeModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>{{ isUserLiked ? '編輯備註' : '收藏用戶' }}</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showLikeModal = false">取消</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <form @submit.prevent="saveLikeUser">
            <ion-item>
              <ion-label position="stacked">備註</ion-label>
              <ion-textarea
                v-model="userNotes"
                placeholder="添加備註，例如：潛在合作夥伴、重要客戶等"
                :rows="6"
              ></ion-textarea>
            </ion-item>
            <div class="ion-padding">
              <ion-button
                type="submit"
                expand="block"
                :disabled="isSubmitting"
              >
                <ion-spinner v-if="isSubmitting" name="crescent"></ion-spinner>
                <span v-else>{{ isUserLiked ? '更新備註' : '收藏用戶' }}</span>
              </ion-button>
              <ion-button
                v-if="isUserLiked"
                expand="block"
                color="danger"
                fill="outline"
                class="ion-margin-top"
                @click="unlikeUser"
                :disabled="isSubmitting"
              >
                <ion-icon :icon="trashOutline" slot="start"></ion-icon>
                取消收藏
              </ion-button>
            </div>
          </form>
        </ion-content>
      </ion-modal>

      <!-- Message Modal (Placeholder) -->
      <ion-modal :is-open="showMessageModal" @didDismiss="showMessageModal = false">
        <ion-header>
          <ion-toolbar>
            <ion-title>發送訊息</ion-title>
            <ion-buttons slot="end">
              <ion-button @click="showMessageModal = false">關閉</ion-button>
            </ion-buttons>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding">
          <div class="message-placeholder">
            <ion-icon :icon="chatbubblesOutline" size="large"></ion-icon>
            <h2>訊息功能即將推出</h2>
            <p>我們正在開發直接訊息功能，敬請期待！</p>
          </div>
        </ion-content>
      </ion-modal>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonBackButton,
  IonButton,
  IonIcon,
  IonSpinner,
  IonToast,
  IonAvatar,
  IonBadge,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonList,
  IonItem,
  IonModal,
  IonTextarea,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  alertCircleOutline,
  heartOutline,
  heart,
  chatbubbleOutline,
  chatbubblesOutline,
  trashOutline,
  timeOutline,
  starOutline,
  personOutline,
  personAddOutline,
  personRemoveOutline,
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import ShopCard from '@/components/ShopCard.vue';
import BranchCard from '@/components/BranchCard.vue';
import EventCard from '@/components/EventCard.vue';
import type { UserRole } from '@/types';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();
const userId = route.params.userId as string;

const userProfile = ref<any>(null);
const userShop = ref<any>(null);
const userBranch = ref<any>(null);
const hostedEvents = ref<any[]>([]);
const interactions = ref<any[]>([]);
const isLoading = ref(true);
const toastMessage = ref('');
const activeTab = ref('info');
const showLikeModal = ref(false);
const showMessageModal = ref(false);
const userNotes = ref('');
const isSubmitting = ref(false);
const isUserLiked = ref(false);
const likedUserData = ref<any>(null);

// Load user profile data
const loadUserProfile = async () => {
  try {
    isLoading.value = true;

    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      toastMessage.value = '請先登入以查看用戶資料';
      router.replace('/login');
      return;
    }

    // Check if user has permission to view profiles (merchant or president only)
    if (authStore.currentUser?.role === 'free') {
      toastMessage.value = '升級會員以查看用戶資料';
      router.replace('/tabs/home');
      return;
    }

    // Check if trying to view own profile
    if (authStore.currentUser?.id === userId) {
      router.replace('/tabs/profile');
      return;
    }

    // Fetch user profile
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        avatar,
        shop:shops(*),
        branch:branches(*),
        branchMemberships:branch_members(
          *,
          branches(*)
        ),
        hostedEvents:events(*),
        eventApplications:event_applications(
          *,
          events(*)
        )
      `)
      .eq('id', userId)
      .single();

    if (userError) throw userError;
    if (!userData) {
      toastMessage.value = '找不到用戶資料';
      return;
    }

    userProfile.value = userData;
    userShop.value = userData.shop;
    userBranch.value = userData.branch;

    // Transform the data to include creator_full_name
    hostedEvents.value = userData.hostedEvents.map((event: any) => ({
      ...event,
      creator_full_name: userData.full_name || null
    }));

    // Log the view interaction
    await logViewInteraction();

    // Load interactions
    await loadInteractions();

    // Check if user is liked
    await checkIfUserLiked();
  } catch (error) {
    console.error('Error loading user profile:', error);
    toastMessage.value = '載入用戶資料時發生錯誤';
  } finally {
    isLoading.value = false;
  }
};

// Log that the current user viewed this profile
const logViewInteraction = async () => {
  if (!authStore.isAuthenticated || !authStore.currentUser?.id) return;

  try {
    await supabase
      .from('user_interaction_logs')
      .insert({
        user_id: authStore.currentUser.id,
        target_user_id: userId,
        action_type: 'view'
      });
  } catch (error) {
    console.error('Error logging view interaction:', error);
    // Non-critical error, don't show toast
  }
};

// Load interactions with this user
const loadInteractions = async () => {
  try {
    // Get interactions from the user_interaction_logs table
    const { data: logsData, error: logsError } = await supabase
      .from('user_interaction_logs')
      .select(`
        *,
        user:user_id(id, full_name, username),
        target:target_user_id(id, full_name, username)
      `)
      .or(`user_id.eq.${userId},target_user_id.eq.${userId}`)
      .order('created_at', { ascending: false });

    if (logsError) throw logsError;

    // Transform the data to a standardized format
    interactions.value = logsData.map(log => {
      const isOutgoing = log.user_id === userId;
      return {
        id: log.id,
        type: log.action_type,
        created_at: log.created_at,
        direction: isOutgoing ? 'outgoing' : 'incoming',
        related_user_id: isOutgoing ? log.target_user_id : log.user_id,
        related_user_name: isOutgoing ? log.target?.full_name : log.user?.full_name,
        notes: log.notes,
        metadata: log.metadata
      };
    });
  } catch (error) {
    console.error('Error loading interactions:', error);
  }
};

// Check if the current user has liked this user
const checkIfUserLiked = async () => {
  if (!authStore.isAuthenticated) return;

  try {
    const { data, error } = await supabase
      .from('user_liked_users')
      .select('*')
      .eq('user_id', authStore.currentUser?.id)
      .eq('liked_user_id', userId)
      .maybeSingle();

    if (error) throw error;

    isUserLiked.value = !!data;
    if (data) {
      likedUserData.value = data;
      userNotes.value = data.notes || '';
    }
  } catch (error) {
    console.error('Error checking if user is liked:', error);
  }
};

// Toggle like status for the user
const toggleLikeUser = () => {
  if (!authStore.isAuthenticated) {
    toastMessage.value = '請先登入';
    return;
  }

  showLikeModal.value = true;
};

// Save like user with notes
const saveLikeUser = async () => {
  if (!authStore.isAuthenticated || !userProfile.value) return;

  try {
    isSubmitting.value = true;

    if (isUserLiked.value) {
      // Update existing like
      const { error } = await supabase
        .from('user_liked_users')
        .update({
          notes: userNotes.value,
          updated_at: new Date().toISOString()
        })
        .eq('id', likedUserData.value.id);

      if (error) throw error;
      toastMessage.value = '備註已更新';
    } else {
      // Add new like
      const { error } = await supabase
        .from('user_liked_users')
        .insert({
          user_id: authStore.currentUser?.id,
          liked_user_id: userId,
          notes: userNotes.value
        });

      if (error) throw error;
      isUserLiked.value = true;
      toastMessage.value = '已收藏用戶';
    }

    // Refresh interactions
    await loadInteractions();
    await checkIfUserLiked();
    showLikeModal.value = false;
  } catch (error) {
    console.error('Error saving user like:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};

// Unlike user
const unlikeUser = async () => {
  if (!authStore.isAuthenticated || !isUserLiked.value) return;

  try {
    isSubmitting.value = true;

    const { error } = await supabase
      .from('user_liked_users')
      .delete()
      .eq('user_id', authStore.currentUser?.id)
      .eq('liked_user_id', userId);

    if (error) throw error;

    isUserLiked.value = false;
    likedUserData.value = null;
    userNotes.value = '';
    toastMessage.value = '已取消收藏用戶';

    // Refresh interactions
    await loadInteractions();
    showLikeModal.value = false;
  } catch (error) {
    console.error('Error unliking user:', error);
    toastMessage.value = '操作失敗，請稍後再試';
  } finally {
    isSubmitting.value = false;
  }
};

// Helper functions
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const formatDateTime = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getRoleLabel = (role: UserRole) => {
  switch (role) {
    case 'free': return '普通會員';
    case 'merchant': return '商家會員';
    case 'president': return '分會長';
    default: return '會員';
  }
};

const getInteractionIcon = (type: string) => {
  switch (type) {
    case 'like': return heart;
    case 'unlike': return heartOutline;
    case 'view': return personOutline;
    case 'message': return chatbubbleOutline;
    default: return timeOutline;
  }
};

const getInteractionColor = (type: string) => {
  switch (type) {
    case 'like': return 'danger';
    case 'unlike': return 'medium';
    case 'view': return 'primary';
    case 'message': return 'success';
    default: return 'medium';
  }
};

const getInteractionTitle = (interaction: any) => {
  const isIncoming = interaction.direction === 'incoming';
  const profileUserName = userProfile.value?.full_name || '用戶';
  const otherUserName = interaction.related_user_name || '用戶';

  // Create a clickable link for a user if they're not the current user
  const getUserLink = (userId: string, userName: string) => {
    if (userId === authStore.currentUser?.id) {
      return userName;
    } else {
      return `<a href="/users/${userId}" class="user-link">${userName}</a>`;
    }
  };

  // Get linked name for the other user if available
  const linkedOtherName = interaction.related_user_id && interaction.related_user_id !== authStore.currentUser?.id
    ? getUserLink(interaction.related_user_id, otherUserName)
    : otherUserName;

  // Get linked name for the profile user
  const linkedProfileName = getUserLink(userId, profileUserName);

  // If the current user is viewing someone else's profile
  if (authStore.currentUser?.id !== userId) {
    if (isIncoming) {
      // The profile owner received an interaction from someone else
      if (interaction.related_user_id === authStore.currentUser?.id) {
        // The current user is the one who performed the action
        switch (interaction.type) {
          case 'like': return `您收藏了 ${linkedProfileName}`;
          case 'unlike': return `您取消收藏 ${linkedProfileName}`;
          case 'view': return `您查看了 ${linkedProfileName} 的資料`;
          case 'message': return `您向 ${linkedProfileName} 發送了訊息`;
          default: return '互動';
        }
      } else {
        // Someone else performed the action
        switch (interaction.type) {
          case 'like': return `${linkedOtherName} 收藏了 ${linkedProfileName}`;
          case 'unlike': return `${linkedOtherName} 取消收藏 ${linkedProfileName}`;
          case 'view': return `${linkedOtherName} 查看了 ${linkedProfileName} 的資料`;
          case 'message': return `${linkedOtherName} 向 ${linkedProfileName} 發送了訊息`;
          default: return '互動';
        }
      }
    } else {
      // The profile owner performed an action on someone else
      if (interaction.related_user_id === authStore.currentUser?.id) {
        // The action was performed on the current user
        switch (interaction.type) {
          case 'like': return `${linkedProfileName} 收藏了您`;
          case 'unlike': return `${linkedProfileName} 取消收藏您`;
          case 'view': return `${linkedProfileName} 查看了您的資料`;
          case 'message': return `${linkedProfileName} 向您發送了訊息`;
          default: return '互動';
        }
      } else {
        // The action was performed on someone else
        switch (interaction.type) {
          case 'like': return `${linkedProfileName} 收藏了 ${linkedOtherName}`;
          case 'unlike': return `${linkedProfileName} 取消收藏 ${linkedOtherName}`;
          case 'view': return `${linkedProfileName} 查看了 ${linkedOtherName} 的資料`;
          case 'message': return `${linkedProfileName} 向 ${linkedOtherName} 發送了訊息`;
          default: return '互動';
        }
      }
    }
  } else {
    // The current user is viewing their own profile
    if (isIncoming) {
      // The current user received an interaction from someone else
      switch (interaction.type) {
        case 'like': return `${linkedOtherName} 收藏了您`;
        case 'unlike': return `${linkedOtherName} 取消收藏您`;
        case 'view': return `${linkedOtherName} 查看了您的資料`;
        case 'message': return `${linkedOtherName} 向您發送了訊息`;
        default: return '互動';
      }
    } else {
      // The current user performed an action on someone else
      switch (interaction.type) {
        case 'like': return `您收藏了 ${linkedOtherName}`;
        case 'unlike': return `您取消收藏 ${linkedOtherName}`;
        case 'view': return `您查看了 ${linkedOtherName} 的資料`;
        case 'message': return `您向 ${linkedOtherName} 發送了訊息`;
        default: return '互動';
      }
    }
  }
};

// Use Ionic lifecycle hooks
onIonViewDidEnter(() => {
  loadUserProfile();
});
</script>

<style scoped>
.loading-container,
.error-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.user-avatar {
  width: 80px;
  height: 80px;
  margin-right: 1rem;
}

.user-info {
  flex: 1;
}

.user-info h1 {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.user-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.industry, .company {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.join-date {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab-content {
  margin-top: 1rem;
}

.section {
  margin-bottom: 2rem;
}

.section h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
}

.empty-text {
  color: var(--ion-color-medium);
  text-align: center;
  padding: 2rem 0;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.timeline {
  position: relative;
  margin: 0 0 2rem 0;
  padding: 0;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 18px;
  height: 100%;
  width: 2px;
  background: var(--ion-color-medium);
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
  padding-left: 45px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ion-color-medium);
  color: white;
  z-index: 1;
}

.timeline-icon.danger {
  background-color: var(--ion-color-danger);
}

.timeline-icon.primary {
  background-color: var(--ion-color-primary);
}

.timeline-icon.success {
  background-color: var(--ion-color-success);
}

.timeline-icon ion-icon {
  font-size: 18px;
}

.timeline-content {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.timeline-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.timeline-date {
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  white-space: nowrap;
  margin-left: 0.5rem;
}

.timeline-notes {
  padding-top: 0.5rem;
  border-top: 1px solid var(--ion-color-light);
  margin-top: 0.5rem;
}

.timeline-notes p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--ion-color-dark);
}

.user-link {
  color: var(--ion-color-primary);
  text-decoration: none;
  font-weight: 500;
}

.user-link:hover {
  text-decoration: underline;
}

.message-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: var(--ion-color-medium);
}

.message-placeholder ion-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
}
</style>
