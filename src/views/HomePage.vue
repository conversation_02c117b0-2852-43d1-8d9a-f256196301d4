<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <LogoImg className="header-logo" slot="start" />
        <ion-buttons slot="end">
          <template v-if="!authStore.isAuthenticated">
            <ion-button router-link="/login">
              登入
            </ion-button>
            <ion-button router-link="/register" fill="solid">
              註冊
            </ion-button>
          </template>
          <template v-else>
            <ion-text class="user-greeting">
              歡迎，{{ userStore.currentUser?.full_name }}
            </ion-text>
          </template>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <!-- Hero section remains outside container for full width -->
        <div class="hero-section">
          <img :src="bannerSrc" alt="Banner" class="hero-banner" />
        </div>

        <!-- TODO: Phase 2 development
        <div class="features-grid ion-padding">
          <ion-card class="feature-card" button>
            <ion-icon :icon="peopleOutline"></ion-icon>
            <h3>人氣商品</h3>
          </ion-card>
          <ion-card class="feature-card" button>
            <ion-icon :icon="trendingUpOutline"></ion-icon>
            <h3>最新任務</h3>
          </ion-card>
          <ion-card class="feature-card" button>
            <ion-icon :icon="flameOutline"></ion-icon>
            <h3>熱門搜索</h3>
          </ion-card>
          <ion-card class="feature-card" button>
            <ion-icon :icon="megaphoneOutline"></ion-icon>
            <h3>必到活動</h3>
          </ion-card>
          <ion-card class="feature-card" button>
            <ion-icon :icon="colorFilterOutline"></ion-icon>
            <h3>合作方案</h3>
          </ion-card>
        </div>-->

        <!-- Latest Shops Section -->
        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">最新商家</h2>
          <ion-button router-link="/shops" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="isLoadingShops" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入商家中...</p>
        </div>

        <div v-else-if="latestShops.length > 0" class="latest-shops ion-padding-horizontal">
          <swiper
            :modules="swiperModules"
            :slides-per-view="1.5"
            :space-between="12"
            :freeMode="{ enabled: true, momentum: false }"
            :cssMode="true"
            :breakpoints="{
              '480': {
                slidesPerView: 2.5,
                spaceBetween: 12,
              },
              '768': {
                slidesPerView: 3.5,
                spaceBetween: 14,
              },
              '1024': {
                slidesPerView: 4.5,
                spaceBetween: 16,
              },
            }"
          >
            <swiper-slide v-for="shop in latestShops" :key="shop.id" style="padding: 4px">
              <ion-card mode="ios" class="shop-card" button :router-link="`/shops/${shop.id}`">
                <div class="shop-banner">
                  <img
                    :src="shop.banner || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8'"
                    :alt="shop.name"
                    loading="lazy"
                    decoding="async"
                  />
                  <div class="loading-placeholder"></div>
                </div>
                <ion-card-header>
                  <div class="shop-info">
                    <img
                      class="shop-logo"
                      :src="shop.logo"
                      :alt="shop.name"
                      loading="lazy"
                      decoding="async"
                      v-if="shop.logo"
                    />
                    <ion-card-title>{{ shop.name }}</ion-card-title>
                  </div>
                  <!--<ion-card-subtitle v-if="shop.description">{{ shop.description }}</ion-card-subtitle>-->
                </ion-card-header>
              </ion-card>
            </swiper-slide>
          </swiper>
        </div>

        <div v-else class="empty-state ion-padding-horizontal">
          <p class="ion-text-center">暫無商家資料</p>
        </div>

        <!-- Latest Products Section -->
        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">最新產品</h2>
          <ion-button router-link="/products" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="isLoadingProducts" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入產品中...</p>
        </div>

        <div v-else-if="latestProducts.length > 0" class="products-section ion-padding-horizontal">
          <swiper
            :modules="swiperModules"
            :slides-per-view="2.1"
            :space-between="12"
            :freeMode="{ enabled: true, momentum: false }"
            :cssMode="true"
            :breakpoints="{
              '480': {
                slidesPerView: 2.5,
                spaceBetween: 12,
              },
              '768': {
                slidesPerView: 3.5,
                spaceBetween: 14,
              },
              '1024': {
                slidesPerView: 5.5,
                spaceBetween: 16,
              },
            }"
          >
            <div class="swiper-button-next products-swiper-button-next" aria-label="Next slide"></div>
            <div class="swiper-button-prev products-swiper-button-prev" aria-label="Previous slide"></div>
            <swiper-slide v-for="product in latestProducts" :key="product.id" style="padding: 4px">
              <ion-card
                mode="ios"
                class="product-card"
                button
                :router-link="`/shops/${product.shop_id}/products/${product.id}`"
              >
                <div class="product-image">
                  <img
                    :src="product.cover_image"
                    :alt="product.title"
                    loading="lazy"
                    decoding="async"
                  />
                  <div class="loading-placeholder"></div>
                  <ion-badge
                    :color="product.is_in_stock ? 'success' : 'medium'"
                    class="stock-badge"
                  >
                    {{ product.is_in_stock ? '有貨' : '缺貨' }}
                  </ion-badge>
                </div>
                <ion-card-header>
                  <div class="shop-info" @click.stop="navigateToShop(product.shop_id)">
                    <img
                      class="shop-logo"
                      :src="product.shops?.logo"
                      :alt="getShopName(product.shop_id)"
                      loading="lazy"
                      decoding="async"
                      v-if="product.shops?.logo"
                    >
                    <span class="shop-name">{{ getShopName(product.shop_id) }}</span>
                  </div>
                  <ion-card-title>{{ product.title }}</ion-card-title>
                  <div class="price-row">
                    <ion-card-subtitle>HK$ {{ product.price }}</ion-card-subtitle>
                  </div>
                </ion-card-header>
              </ion-card>
            </swiper-slide>
          </swiper>
        </div>

        <div v-else class="empty-state ion-padding-horizontal">
          <p class="ion-text-center">暫無產品資料</p>
        </div>

        <!-- Featured Branches Section -->
        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">精選分會</h2>
          <ion-button router-link="/branches" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="isLoadingBranches" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入分會中...</p>
        </div>

        <div v-else-if="featuredBranches.length > 0" class="featured-branches ion-padding-horizontal">
          <swiper
            :modules="swiperModules"
            :slides-per-view="1.5"
            :space-between="12"
            :freeMode="{ enabled: true, momentum: false }"
            :cssMode="true"
            :breakpoints="{
              '480': {
                slidesPerView: 2.5,
                spaceBetween: 12,
              },
              '768': {
                slidesPerView: 3.5,
                spaceBetween: 14,
              },
              '1024': {
                slidesPerView: 4.5,
                spaceBetween: 16,
              },
            }"
          >
            <swiper-slide v-for="branch in featuredBranches" :key="branch.id" style="padding: 4px">
              <BranchCard
                :branch="branch"
                :showJoinButton="false"
              />
            </swiper-slide>
          </swiper>
        </div>

        <div v-else class="empty-state ion-padding-horizontal">
          <p class="ion-text-center">暫無精選分會資料</p>
        </div>

        <!-- Popular Branches Section -->
        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">熱門分會</h2>
          <ion-button router-link="/branches" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="isLoadingBranches" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入分會中...</p>
        </div>

        <div v-else-if="latestBranches.length > 0" class="latest-branches ion-padding-horizontal">
          <swiper
            :modules="swiperModules"
            :slides-per-view="1.5"
            :space-between="12"
            :freeMode="{ enabled: true, momentum: false }"
            :cssMode="true"
            :breakpoints="{
              '480': {
                slidesPerView: 2.5,
                spaceBetween: 12,
              },
              '768': {
                slidesPerView: 3.5,
                spaceBetween: 14,
              },
              '1024': {
                slidesPerView: 4.5,
                spaceBetween: 16,
              },
            }"
          >
            <swiper-slide v-for="branch in latestBranches" :key="branch.id" style="padding: 4px">
              <BranchCard
                :branch="branch"
                :showJoinButton="false"
              />
            </swiper-slide>
          </swiper>
        </div>

        <div v-else class="empty-state ion-padding-horizontal">
          <p class="ion-text-center">暫無分會資料</p>
        </div>

        <!-- Latest Organizations Section -->
        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">最新組織</h2>
          <ion-button router-link="/organizations" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="isLoadingOrganizations" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入組織中...</p>
        </div>

        <div v-else-if="latestOrganizations.length > 0" class="latest-organizations ion-padding-horizontal">
          <swiper
            :modules="swiperModules"
            :slides-per-view="1.5"
            :space-between="12"
            :freeMode="{ enabled: true, momentum: false }"
            :cssMode="true"
            :breakpoints="{
              '480': {
                slidesPerView: 2.5,
                spaceBetween: 12,
              },
              '768': {
                slidesPerView: 3.5,
                spaceBetween: 14,
              },
              '1024': {
                slidesPerView: 4.5,
                spaceBetween: 16,
              },
            }"
          >
            <swiper-slide v-for="organization in latestOrganizations" :key="organization.id" style="padding: 4px">
              <OrganizationCard
                :organization="organization"
                :is-member="false"
              />
            </swiper-slide>
          </swiper>
        </div>

        <div v-else class="empty-state ion-padding-horizontal">
          <p class="ion-text-center">暫無組織資料</p>
        </div>

        <!-- Update Debug Panel (only shows in dev mode or with ?debug=true) -->
        <UpdateDebugPanel />

        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">即將舉行的活動</h2>
          <ion-button router-link="/events?tab=future" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="eventsStore.isLoading" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入活動中...</p>
        </div>

        <ion-list v-else-if="eventsStore.upcomingEvents.length > 0" class="event-list ion-padding">
          <ion-item v-for="event in eventsStore.upcomingEvents.slice(0, 3)" :key="event.id" button detail :router-link="`/events/${event.id}`">
            <ion-thumbnail slot="start">
              <img :src="event.banner_photo || 'https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?w=800'" :alt="event.title" />
            </ion-thumbnail>
            <ion-label>
              <h2>{{ event.title }}</h2>
              <p>{{ formatDate(event.start_datetime) }}</p>
              <p>{{ event.address }}</p>
            </ion-label>
          </ion-item>
        </ion-list>

        <ion-list v-else class="event-list ion-padding">
          <ion-item lines="none">
            <ion-label class="ion-text-center">
              <p>暫無即將舉行的活動</p>
            </ion-label>
          </ion-item>
        </ion-list>

        <div class="section-header ion-padding-horizontal">
          <h2 class="section-title">歷史活動</h2>
          <ion-button router-link="/events?tab=past" fill="clear" class="view-more-btn">
            查看更多
          </ion-button>
        </div>

        <div v-if="eventsStore.isLoading" class="loading-container">
          <ion-spinner name="crescent"></ion-spinner>
          <p>載入活動中...</p>
        </div>

        <ion-list v-else-if="eventsStore.pastEvents.length > 0" class="event-list ion-padding">
          <ion-item v-for="event in eventsStore.pastEvents.slice(0, 3)" :key="event.id" button detail :router-link="`/events/${event.id}`">
            <ion-thumbnail slot="start">
              <img :src="event.banner_photo || 'https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?w=800'" :alt="event.title" />
            </ion-thumbnail>
            <ion-label>
              <h2>{{ event.title }}</h2>
              <p>{{ formatDate(event.start_datetime) }}</p>
              <p>{{ event.address }}</p>
            </ion-label>
          </ion-item>
        </ion-list>

        <ion-list v-else class="event-list ion-padding">
          <ion-item lines="none">
            <ion-label class="ion-text-center">
              <p>暫無歷史活動</p>
            </ion-label>
          </ion-item>
        </ion-list>

        <!--
        <div class="stats-section ion-padding">
          <ion-grid>
            <ion-row>
              <ion-col size="6">
                <div class="stat-card">
                  <ion-icon :icon="peopleOutline" color="primary"></ion-icon>
                  <h3>1,234+</h3>
                  <p>活躍會員</p>
                </div>
              </ion-col>
              <ion-col size="6">
                <div class="stat-card">
                  <ion-icon :icon="storefrontOutline" color="secondary"></ion-icon>
                  <h3>50+</h3>
                  <p>商家夥伴</p>
                </div>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>
        -->
      </div>

      <ion-toast
        :is-open="!!toastMessage"
        :message="toastMessage"
        :duration="3000"
        @didDismiss="toastMessage = ''"
      ></ion-toast>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonCard,
  IonCardHeader,
  IonCardTitle, IonCardSubtitle,
  IonContent,
  IonButton,
  IonList,
  IonItem,
  IonThumbnail,
  IonLabel,
  IonButtons,
  IonText,
  IonBadge,
  IonSpinner,
  IonToast,
  onIonViewDidEnter,
} from '@ionic/vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Virtual, FreeMode, Pagination, A11y, Navigation, Mousewheel } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import LogoImg from '@/components/LogoImg.vue';
import BranchCard from '@/components/BranchCard.vue';
import OrganizationCard from '@/components/OrganizationCard.vue';
import UpdateDebugPanel from '@/components/UpdateDebugPanel.vue';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { useEventsStore } from '@/stores/events';
import { useOrganizationStore } from '@/stores/organization';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'vue-router';

import bannerSrc from '@/assets/banner.jpeg';

const authStore = useAuthStore();
const userStore = useUserStore();
const eventsStore = useEventsStore();
const organizationStore = useOrganizationStore();
const router = useRouter();
const swiperModules = [Virtual, FreeMode, Pagination, A11y, Navigation, Mousewheel];
const latestShops = ref<any[]>([]);
const latestProducts = ref<any[]>([]);
const latestBranches = ref<any[]>([]);
const featuredBranches = ref<any[]>([]);
const latestOrganizations = ref<any[]>([]);
const shopNames = ref(new Map<string, string>());
const isLoadingShops = ref(false);
const isLoadingProducts = ref(false);
const isLoadingBranches = ref(false);
const isLoadingOrganizations = ref(false);
const toastMessage = ref('');

onIonViewDidEnter(async () => {
  // Fetch events data
  if (eventsStore.events.length === 0) {
    await eventsStore.fetchEvents();
  }

  try {
    isLoadingShops.value = true;
    isLoadingProducts.value = true;
    isLoadingBranches.value = true;

    // Load latest shops
    const { data: shops, error: shopsError } = await supabase
      .from('shops')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (shopsError) throw shopsError;
    latestShops.value = shops || [];
    isLoadingShops.value = false;

    // Load latest products with shop info
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        shops (
          id,
          name,
          logo
        )
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(15);

    if (productsError) throw productsError;
    latestProducts.value = products || [];

    // Create shop names map
    shopNames.value = new Map(
      products
        ?.filter(p => p.shops)
        .map(p => [p.shop_id, p.shops.name]) || []
    );

    // First, load all branches we need
    // Load featured branches
    const branchQuery = `
      *,
      owner:owner_id (
        id,
        full_name,
        username
      )
    `;
    const { data: featuredData, error: featuredError } = await supabase
      .from('branches')
      .select(branchQuery)
      .eq('is_featured', true)
      .order('created_at', { ascending: false });

    if (featuredError) throw featuredError;

    // Load popular branches
    const { data: branches, error: branchesError } = await supabase
      .from('branches')
      .select(branchQuery)
      .order('member_count', { ascending: false })
      .limit(10);

    if (branchesError) throw branchesError;

    featuredBranches.value = featuredData || [];
    latestBranches.value = branches || [];
  } catch (error) {
    console.error('Error loading data:', error);
  } finally {
    isLoadingProducts.value = false;
    isLoadingBranches.value = false;

    // Load latest organizations
    try {
      isLoadingOrganizations.value = true;
      await organizationStore.fetchOrganizations();
      latestOrganizations.value = organizationStore.organizations.slice(0, 10);
    } catch (error) {
      console.error('Error loading organizations:', error);
    } finally {
      isLoadingOrganizations.value = false;
    }
  }
});

const getShopName = (shopId: string) => {
  return shopNames.value.get(shopId) || '未知商家';
};

const navigateToShop = (shopId: string) => {
  router.push(`/shops/${shopId}`);
};

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  });
};
</script>

<style scoped>
.header-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-left: 1rem;
}

.user-greeting {
  margin-right: 1rem;
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.hero-section {
  position: relative;
  height: 60vh;
  min-height: 400px;
  max-height: 600px;
  overflow: hidden;
}

.hero-banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.feature-card {
  margin: 0;
  padding: 1rem;
  text-align: center;
  border-radius: 16px;
  background: var(--ion-color-light);
}

.feature-card ion-icon {
  font-size: 2rem;
  color: var(--ion-color-primary);
  margin-bottom: 0.5rem;
}

.feature-card h3 {
  font-size: 0.9rem;
  margin: 0;
  color: var(--ion-color-dark);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0 1rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0;
}

.view-more-btn {
  font-size: 0.9rem;
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  height: 2rem;
  color: var(--ion-color-primary);
}

.latest-shops,
.latest-branches,
.featured-products {
  margin-bottom: 2rem;
}

.shop-card,
.branch-card {
  margin: 0;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.shop-card:hover,
.branch-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.shop-banner,
.branch-banner {
  height: 140px;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}

.shop-banner img,
.branch-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
  will-change: transform;
  max-width: 100%;
}

@media (hover: hover) {
  .shop-card:hover .shop-banner img,
  .branch-card:hover .branch-banner img {
    transform: scale(1.05);
  }
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.shop-logo,
.branch-logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--ion-color-light);
  background-color: #f5f5f5;
  will-change: transform;
  max-width: 100%;
}

.branch-details {
  flex: 1;
}

.branch-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  margin-bottom: 4px;
}

.member-count,
.district,
.owner-name,
.created-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.branch-activity-level {
  margin-top: 6px;
}

.branch-activity-level span {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.activity-low {
  background-color: rgba(var(--ion-color-danger-rgb), 0.15);
  color: var(--ion-color-danger);
}

.activity-medium {
  background-color: rgba(var(--ion-color-warning-rgb), 0.15);
  color: var(--ion-color-warning-shade);
}

.activity-high {
  background-color: rgba(var(--ion-color-success-rgb), 0.15);
  color: var(--ion-color-success);
}

.activity-unknown {
  background-color: rgba(var(--ion-color-medium-rgb), 0.15);
  color: var(--ion-color-medium);
}

.featured-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--ion-color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 10;
}

ion-card-header {
  padding: 12px 16px;
}

ion-card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.products-section {
  margin-bottom: 2rem;
}

.product-card {
  margin: 0;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  background: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
  will-change: transform;
  max-width: 100%;
}

@media (hover: hover) {
  .product-card:hover .product-image img {
    transform: scale(1.05);
  }
}

.stock-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  border-radius: 20px;
  padding: 4px 8px;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.shop-info:hover {
  background-color: var(--ion-color-light);
}

.price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.event-list ion-thumbnail {
  width: 120px;
  height: 80px;
}

.event-list ion-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.stat-card {
  background: var(--ion-card-background);
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: var(--ion-card-box-shadow);
}

.stat-card ion-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0.5rem 0;
  color: var(--ion-text-color);
}

.stat-card p {
  color: var(--ion-color-medium);
  margin: 0;
}

:root {
  --swiper-theme-color: var(--ion-color-primary);
}

.swiper {
  width: 100%;
  height: 100%;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.swiper-slide {
  height: auto;
  will-change: transform;
  transform: translateZ(0);
}

.loading-placeholder {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--ion-color-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: loading-spin 1s infinite linear;
  opacity: 0;
}

img:not([src]), img[src=""] {
  visibility: hidden;
}

img:not([src]) + .loading-placeholder,
img[src=""] + .loading-placeholder {
  opacity: 1;
}

@keyframes loading-spin {
  100% {
    transform: rotate(360deg);
  }
}

/* Loading and empty states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 16px;
  margin: 0 1rem 1.5rem;
}

.loading-container ion-spinner {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  color: var(--ion-color-primary);
}

.loading-container p {
  color: var(--ion-color-medium);
  margin: 0;
  font-size: 0.9rem;
}

.empty-state {
  padding: 2rem;
  background: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 16px;
  margin: 0 1rem 1.5rem;
}

.empty-state p {
  color: var(--ion-color-medium);
  margin: 0;
  font-size: 0.9rem;
}

.view-more-item {
  --background: transparent;
  --border-color: transparent;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .hero-section {
    height: 40vh;
    min-height: 300px;
  }

  .section-header {
    margin: 1.25rem 0 0.75rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .view-more-btn {
    font-size: 0.8rem;
  }

  .shop-banner {
    height: 140px;
  }

  .shop-logo {
    width: 28px;
    height: 28px;
  }

  .loading-container {
    padding: 1.5rem;
    margin: 0 0.5rem 1rem;
  }

  .empty-state {
    padding: 1.5rem;
    margin: 0 0.5rem 1rem;
  }

  /* Optimize for mobile performance */
  .product-image {
    height: 180px;
  }

  .swiper-slide {
    contain: content;
  }

  .shop-card, .product-card, .branch-card {
    contain: content;
  }

  /* Reduce animation complexity on mobile */
  .shop-card:hover, .product-card:hover, .branch-card:hover {
    transform: none;
  }

  .shop-card:hover .shop-banner img,
  .product-card:hover .product-image img,
  .branch-card:hover .branch-banner img {
    transform: none;
  }
}
</style>