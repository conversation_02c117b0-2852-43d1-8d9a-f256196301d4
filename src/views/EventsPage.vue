<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>活動</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="showCreateEventModal = true" v-if="userStore.currentUser?.role == 'president'">
            <ion-icon slot="start" :icon="addOutline"></ion-icon>
            新增活動
          </ion-button>
          <!-- Toggle View Button -->
          <ion-button @click="toggleView">
            <ion-icon :icon="isCalendarView ? listOutline : calendarClearOutline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <div class="page-container">
        <ion-header collapse="condense">
          <ion-toolbar>
            <ion-title size="large">活動</ion-title>
          </ion-toolbar>
        </ion-header>

        <!-- View Selector -->
        <div class="view-selector">
          <ion-segment v-if="!isCalendarView" v-model="selectedSegment" mode="ios">
            <ion-segment-button value="future">
              <ion-label>即將舉行</ion-label>
            </ion-segment-button>
            <ion-segment-button value="past">
              <ion-label>已結束</ion-label>
            </ion-segment-button>
            <ion-segment-button value="registered" v-if="authStore.isAuthenticated">
              <ion-label>已報名</ion-label>
            </ion-segment-button>
            <ion-segment-button value="hosted" v-if="authStore.isAuthenticated">
              <ion-label>由我主辦</ion-label>
            </ion-segment-button>
            <ion-segment-button value="liked" v-if="authStore.isAuthenticated && likedEvents.length > 0">
              <ion-label>已收藏</ion-label>
            </ion-segment-button>
          </ion-segment>
        </div>

        <!-- Calendar View -->
        <div v-if="isCalendarView" class="calendar-container">
          <ScheduleXCalendar :calendar-app="calendarApp" />

          <!-- Events List for Selected Date -->
          <div class="events-list">
            <h3 class="date-header">{{ formatSelectedDate }}</h3>
            <ion-list>
              <ion-item v-for="event in selectedDateEvents" :key="event.id" button @click="navigateToEvent(event)" class="event-item">
                <ion-thumbnail slot="start" v-if="event.banner_photo">
                  <img :src="event.banner_photo" :alt="event.title" />
                </ion-thumbnail>
                <ion-label>
                  <h2>{{ event.title }}</h2>
                  <div class="event-details">
                    <div class="event-time">
                      <ion-icon :icon="timeOutline" color="medium"></ion-icon>
                      <span>{{ formatDateTimeDisplay(event.start_datetime) }}</span>
                    </div>
                    <div class="event-time">
                      <ion-icon :icon="timeOutline" color="medium"></ion-icon>
                      <span>至 {{ formatDateTimeDisplay(event.end_datetime) }}</span>
                    </div>
                    <div class="event-location">
                      <ion-icon :icon="locationOutline" color="medium"></ion-icon>
                      <span>{{ event.address }}</span>
                    </div>
                  </div>
                </ion-label>
              </ion-item>
            </ion-list>

            <div v-if="selectedDateEvents.length === 0" class="no-events">
              <ion-icon :icon="calendarOutline" color="medium" size="large"></ion-icon>
              <p>當日沒有活動</p>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div v-else class="list-view">
          <!-- Future Events -->
          <div v-if="selectedSegment === 'future'">
            <div v-if="upcomingEvents.length === 0" class="empty-state">
              <ion-icon :icon="calendarOutline" color="medium" size="large"></ion-icon>
              <p>暫時沒有即將舉行的活動</p>
            </div>

            <div v-else class="events-grid">
              <EventCard v-for="event in upcomingEvents" :key="event.id" :event="event" />
            </div>
          </div>

          <!-- Past Events -->
          <div v-if="selectedSegment === 'past'">
            <div v-if="pastEvents.length === 0" class="empty-state">
              <ion-icon :icon="calendarOutline" color="medium" size="large"></ion-icon>
              <p>暫時沒有已結束的活動</p>
            </div>

            <div v-else class="events-grid">
              <EventCard v-for="event in pastEvents" :key="event.id" :event="event" />
            </div>
          </div>

          <!-- Registered Events -->
          <div v-if="selectedSegment === 'registered' && authStore.isAuthenticated">
            <LoadingSpinner v-if="isLoading" />

            <div v-else>
              <ion-list>
                <ion-item-group>
                  <ion-item-divider sticky>
                    <ion-label>即將舉行</ion-label>
                  </ion-item-divider>

                  <ion-item v-for="application in upcomingApplications" :key="application.id" class="event-item"
                            @click="viewEvent(application.events)" button detail>
                    <ion-thumbnail slot="start" v-if="application.events.banner_photo">
                      <img :src="application.events.banner_photo" :alt="application.events.title" />
                    </ion-thumbnail>
                    <ion-label>
                      <h2>{{ application.events.title }}</h2>
                      <p>{{ formatDateTimeDisplay(application.events.start_datetime) }}</p>
                      <p>{{ application.events.address }}</p>
                    </ion-label>
                    <div class="event-actions" slot="end">
                      <ion-button fill="clear" @click.stop="viewQRCode(application)">
                        <ion-icon :icon="qrCodeOutline" color="primary"></ion-icon>
                      </ion-button>
                    </div>
                  </ion-item>
                </ion-item-group>

                <ion-item-group>
                  <ion-item-divider sticky>
                    <ion-label>已結束</ion-label>
                  </ion-item-divider>

                  <ion-item v-for="application in pastApplications" :key="application.id" class="event-item"
                            @click="viewEvent(application.events)" button detail>
                    <ion-thumbnail slot="start" v-if="application.events.banner_photo">
                      <img :src="application.events.banner_photo" :alt="application.events.title" />
                    </ion-thumbnail>
                    <ion-label>
                      <h2>{{ application.events.title }}</h2>
                      <p>{{ formatDateTimeDisplay(application.events.start_datetime) }}</p>
                      <p>{{ application.events.address }}</p>
                    </ion-label>
                  </ion-item>
                </ion-item-group>
              </ion-list>

              <div v-if="!isLoading && applications.length === 0" class="empty-state">
                <ion-icon :icon="calendarOutline" color="medium"></ion-icon>
                <p>您還沒有報名任何活動</p>
                <ion-button @click="selectedSegment = 'future'">
                  瀏覽活動
                  <ion-icon :icon="arrowForward" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>
          </div>

          <!-- Hosted Events -->
          <div v-if="selectedSegment === 'hosted' && authStore.isAuthenticated">
            <LoadingSpinner v-if="isLoading" />

            <div v-else>
              <ion-list>
                <ion-item-group>
                  <ion-item-divider sticky>
                    <ion-label>即將舉行</ion-label>
                  </ion-item-divider>

                  <ion-item v-for="event in upcomingHostedEvents" :key="event.id" class="event-item"
                            @click="viewEvent(event)" button detail>
                    <ion-thumbnail slot="start" v-if="event.banner_photo">
                      <img :src="event.banner_photo" :alt="event.title" />
                    </ion-thumbnail>
                    <ion-label>
                      <h2>{{ event.title }}</h2>
                      <p>{{ formatDateTimeDisplay(event.start_datetime) }}</p>
                      <p>{{ event.address }}</p>
                      <p class="attendance-info">
                        已報名：{{ event.applicationCount || 0 }} 人
                        <span v-if="event.max_participants">
                          / 上限 {{ event.max_participants }} 人
                        </span>
                      </p>
                    </ion-label>
                    <div class="event-actions" slot="end">
                      <ion-button fill="clear" @click.stop="viewApplications(event)">
                        <ion-icon :icon="peopleOutline" color="primary"></ion-icon>
                      </ion-button>
                    </div>
                  </ion-item>
                </ion-item-group>

                <ion-item-group>
                  <ion-item-divider sticky>
                    <ion-label>已結束</ion-label>
                  </ion-item-divider>

                  <ion-item v-for="event in pastHostedEvents" :key="event.id" class="event-item"
                            @click="viewEvent(event)" button detail>
                    <ion-thumbnail slot="start" v-if="event.banner_photo">
                      <img :src="event.banner_photo" :alt="event.title" />
                    </ion-thumbnail>
                    <ion-label>
                      <h2>{{ event.title }}</h2>
                      <p>{{ formatDateTimeDisplay(event.start_datetime) }}</p>
                      <p>{{ event.address }}</p>
                      <p class="attendance-info">
                        參與人數：{{ event.attendedCount || 0 }} 人
                      </p>
                    </ion-label>
                    <div class="event-actions" slot="end">
                      <ion-button fill="clear" @click.stop="viewApplications(event)">
                        <ion-icon :icon="peopleOutline" color="primary"></ion-icon>
                      </ion-button>
                    </div>
                  </ion-item>
                </ion-item-group>
              </ion-list>

              <div v-if="!isLoading && hostedEvents.length === 0" class="empty-state">
                <ion-icon :icon="calendarOutline" color="medium"></ion-icon>
                <p>您還沒有主辦任何活動</p>
                <ion-button @click="showCreateEventModal = true" v-if="userStore.currentUser?.role == 'president'">
                  建立活動
                  <ion-icon :icon="addOutline" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>
          </div>

          <!-- Liked Events -->
          <div v-if="selectedSegment === 'liked' && authStore.isAuthenticated">
            <LoadingSpinner v-if="isLoading" />

            <div v-else>
              <div v-if="likedEvents.length === 0" class="empty-state">
                <ion-icon :icon="heartOutline" color="medium" size="large"></ion-icon>
                <p>您還沒有收藏任何活動</p>
                <ion-button @click="selectedSegment = 'future'">
                  瀏覽活動
                  <ion-icon :icon="arrowForward" slot="end"></ion-icon>
                </ion-button>
              </div>

              <div v-else class="events-grid">
                <EventCard v-for="event in likedEvents" :key="event.id" :event="event" />
              </div>
            </div>
          </div>
        </div>

        <!-- Create Event Modal -->
        <ion-modal :is-open="showCreateEventModal" @didDismiss="closeCreateEventModal">
          <ion-header>
            <ion-toolbar>
              <ion-title>建立活動</ion-title>
              <ion-buttons slot="end">
                <ion-button @click="closeCreateEventModal">取消</ion-button>
              </ion-buttons>
            </ion-toolbar>
          </ion-header>
          <ion-content class="ion-padding">
            <EventForm
              :initial-data="newEvent"
              mode="create"
              :is-submitting="isSubmitting"
              :banner-preview="bannerPreview"
              :min-start-date="tomorrow"
              @update:initial-data="newEvent = $event"
              @submit="showCreateConfirmation"
              @take-banner="takeBanner"
              @remove-banner="removeBanner"
            />
          </ion-content>
        </ion-modal>

        <!-- Add confirmation alert -->
        <ion-alert
          :is-open="showConfirmation"
          header="確認建立活動"
          :message="confirmationMessage"
          :buttons="[
            {
              text: '取消',
              role: 'cancel',
              handler: () => {
                showConfirmation = false;
              },
            },
            {
              text: '確定',
              handler: () => {
                showConfirmation = false;
                handleCreateEvent();
              },
            },
          ]"
        ></ion-alert>

        <!-- QR Code Modal -->
        <ion-modal :is-open="!!selectedApplication" @didDismiss="selectedApplication = null">
          <ion-header>
            <ion-toolbar>
              <ion-title>入場二維碼</ion-title>
              <ion-buttons slot="end">
                <ion-button @click="selectedApplication = null">關閉</ion-button>
              </ion-buttons>
            </ion-toolbar>
          </ion-header>
          <ion-content class="ion-padding">
            <div class="qr-code-content">
              <h2>{{ selectedApplication?.events.title }}</h2>
              <p>{{ formatDateTimeDisplay(selectedApplication?.events.start_datetime) }}</p>
              <p>至 {{ formatDateTimeDisplay(selectedApplication?.events.end_datetime) }}</p>

              <div class="qr-code">
                <img :src="qrCodeUrl" alt="QR Code" />
                <div class="check-in-code">
                  <p>簽到碼：<span>{{ formatCheckInCode(extractCheckInCode(selectedApplication?.qr_code || '')) }}</span></p>
                </div>
              </div>

              <p class="qr-note">請於活動當天出示此二維碼以供登記出席</p>
            </div>
          </ion-content>
        </ion-modal>

        <!-- Applications Modal -->
        <ion-modal :is-open="!!selectedEvent" @didDismiss="selectedEvent = null">
          <ion-header>
            <ion-toolbar>
              <ion-title>報名名單</ion-title>
              <ion-buttons slot="end">
                <ion-button @click="selectedEvent = null">關閉</ion-button>
              </ion-buttons>
            </ion-toolbar>
          </ion-header>
          <ion-content class="ion-padding">
            <div class="applications-list">
              <h2>{{ selectedEvent?.title }}</h2>
              <p>{{ formatDateTimeDisplay(selectedEvent?.start_datetime) }}</p>

              <ion-list>
                <ion-item v-for="application in eventApplications" :key="application.id">
                  <ion-label>
                    <h2>{{ application.full_name }}</h2>
                    <p>{{ application.email }}</p>
                    <p>{{ application.phone }}</p>
                  </ion-label>
                  <div class="application-actions" slot="end">
                    <ion-badge :color="getStatusColor(application.status)">
                      {{ getStatusLabel(application.status) }}
                    </ion-badge>
                  </div>
                </ion-item>
              </ion-list>
            </div>
          </ion-content>
        </ion-modal>

        <!-- Toast Messages -->
        <ion-toast
          :is-open="!!toastMessage"
          :message="toastMessage"
          :duration="3000"
          @didDismiss="toastMessage = ''"
        ></ion-toast>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { extractCheckInCode, formatCheckInCode } from '@/utils/qrCodeUtils';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonList,
  IonItem,
  IonItemDivider,
  IonItemGroup,
  IonThumbnail,
  IonLabel,
  IonModal,
  IonBadge,
  IonButtons,
  IonButton,
  IonIcon,
  IonInput,
  IonTextarea,
  IonToast,
  IonAlert,
  IonDatetime,
  IonDatetimeButton,
  IonSegment,
  IonSegmentButton,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  onIonViewDidEnter,
} from '@ionic/vue';
import {
  trashOutline,
  addOutline,
  personOutline,
  calendarOutline,
  timeOutline,
  locationOutline,
  listOutline,
  calendarClearOutline,
  cameraOutline,
  eyeOutline,
  qrCodeOutline,
  peopleOutline,
  createOutline,
  mailOutline,
  arrowForward,
  heart,
  heartOutline,
} from 'ionicons/icons';
import { useAuthStore } from '@/stores/auth';
import { useUserStore } from '@/stores/user';
import { useEventsStore } from '@/stores/events';
import { api } from '@/services/api';
import type { Event, CreateEvent } from '@/services/schema';
import { supabase } from '@/lib/supabase';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import { ScheduleXCalendar } from '@schedule-x/vue';
import { createCalendarControlsPlugin } from "@schedule-x/calendar-controls";
import {
  createCalendar,
  createViewDay,
  createViewMonthAgenda,
  createViewMonthGrid,
  createViewWeek,
  viewMonthAgenda,
  viewMonthGrid,
  viewWeek
} from '@schedule-x/calendar';
import '@schedule-x/theme-default/dist/index.css';
import { uploadImages } from '@/lib/cloudflare';
import { usePhotoGallery, Photo } from '@/composables/usePhotoGallery';
import { utils } from '@/composables/utils';
import EventForm from '@/components/EventForm.vue';
import EventCard from '@/components/EventCard.vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const userStore = useUserStore();
const eventsStore = useEventsStore();
const events = ref<Event[]>([]);
const applications = ref<any[]>([]);
const hostedEvents = ref<any[]>([]);
const likedEvents = ref<any[]>([]);
const showCreateEventModal = ref(false);
const isSubmitting = ref(false);
const toastMessage = ref('');
const showConfirmation = ref(false);
const selectedDate = ref(new Date());
const isCalendarView = ref(false);
const selectedSegment = ref('future');
const selectedApplication = ref<any>(null);
const selectedEvent = ref<any>(null);
const eventApplications = ref<any[]>([]);
const isLoading = ref(true);

// For QR code display
const qrCodeUrl = computed(() => {
  if (!selectedApplication.value?.qr_code) return '';
  return `https://qrcode.tec-it.com/API/QRCode?data=${encodeURIComponent(selectedApplication.value.qr_code)}&dpi=96&quietzone=5`;
});

// Initialize photo gallery
const { takePhoto } = usePhotoGallery();

// Image upload refs
const bannerPreview = ref<string | null>(null);
const bannerPhoto = ref<Photo | null>(null);

// Calendar configuration
const calendarControls = createCalendarControlsPlugin();
const calendarApp = createCalendar({
  selectedDate: new Date().toISOString().split('T')[0],
  locale: 'zh-HK',
  views: [
    createViewMonthGrid(),
    createViewMonthAgenda(),
  ],
  defaultView: 'month-grid',
  events: [],
  theme: {
    primary: '#6B4593',
    accent: '#FF1B85',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    fontSize: '16px',
    borderRadius: '8px',
    dateRangeEnd: '#FF1B85',
    dateRangeStart: '#6B4593',
  },
  callbacks: {
    onClickDate(date) {
      selectedDate.value = new Date(date);
    },
    onEventClick: (event: any) => {
      const foundEvent = events.value.find(e => e.id === event.id);
      if (foundEvent) {
        navigateToEvent(foundEvent);
        selectedDate.value = new Date(event.start);
      }
    },
  }
});

// Transform events for calendar
const updateCalendarEvents = () => {
  const calendarEvents = events.value.map(event => ({
    id: event.id,
    title: event.title,
    start: event.start_datetime,
    end: event.end_datetime,
    color: '#6B4593',
    allDay: false,
  }));
  calendarApp.events.set(calendarEvents);
};

// Get events for selected date
const selectedDateEvents = computed(() => {
  return events.value.filter(event => {
    const eventDate = new Date(event.start_datetime);
    return (
      eventDate.getFullYear() === selectedDate.value.getFullYear() &&
      eventDate.getMonth() === selectedDate.value.getMonth() &&
      eventDate.getDate() === selectedDate.value.getDate()
    );
  });
});

// Get upcoming events
const upcomingEvents = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison

  return events.value
    .filter(event => new Date(event.start_datetime).getTime() >= today.getTime())
    .sort((a, b) => new Date(a.start_datetime).getTime() - new Date(b.start_datetime).getTime());
});

// Get past events
const pastEvents = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison

  return events.value
    .filter(event => new Date(event.start_datetime) < today)
    .sort((a, b) => new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime()); // Sort by most recent first
});

// Get upcoming registered events
const upcomingApplications = computed(() => {
  return applications.value.filter(app => new Date(app.events.start_datetime) >= new Date());
});

// Get past registered events
const pastApplications = computed(() => {
  return applications.value.filter(app => new Date(app.events.start_datetime) < new Date());
});

// Get upcoming hosted events
const upcomingHostedEvents = computed(() => {
  return hostedEvents.value.filter(event => new Date(event.start_datetime) >= new Date());
});

// Get past hosted events
const pastHostedEvents = computed(() => {
  return hostedEvents.value.filter(event => new Date(event.start_datetime) < new Date());
});

const formatSelectedDate = computed(() => {
  return selectedDate.value.toLocaleDateString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  });
});

// Prefilled event data for testing
const tomorrow = computed(() => {
  const date = new Date();
  date.setDate(date.getDate() + 1);
  return date.toISOString().split('T')[0];
});
const newEvent = ref<Partial<CreateEvent>>({
  title: '',
  start_datetime: tomorrow.value + 'T14:00:00',
  end_datetime: tomorrow.value + 'T17:00:00',
  address: '',
  description: '',
  banner_photo: '',
  max_participants: 100
});

// Format datetime for display (assuming input is already in HKT)
const formatDateTimeForDisplay = (dateTimeStr: string) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-HK', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

// Alias for consistency in naming
const formatDateTimeDisplay = formatDateTimeForDisplay;

const confirmationMessage = computed(() => {
  const startDateTime = formatDateTimeForDisplay(newEvent.value.start_datetime || '');
  const endDateTime = formatDateTimeForDisplay(newEvent.value.end_datetime || '');

  return `請確認以下活動資料：<br /><br />
活動標題：${newEvent.value.title}<br />
開始時間：${startDateTime}<br />
結束時間：${endDateTime}<br />
地點：${newEvent.value.address}<br />
人數上限：${newEvent.value.max_participants || '無限制'}<br /><br />
活動描述：<br />
${newEvent.value.description}`;
});

onIonViewDidEnter(async () => {
  isLoading.value = true;
  await loadEvents();

  if (authStore.isAuthenticated) {
    await Promise.all([
      loadApplications(),
      loadHostedEvents(),
      loadLikedEvents()
    ]);
  }
  isLoading.value = false;

  // Handle tab query parameter
  if (route.query.tab) {
    const tab = route.query.tab as string;
    if (['future', 'past', 'registered', 'hosted', 'liked'].includes(tab)) {
      selectedSegment.value = tab;
      isCalendarView.value = false;
      router.replace({ path: route.path });
    }
  }
});

const loadEvents = async () => {
  try {
    // Fetch events with creator information
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        users:user_id (
          full_name
        )
      `)
      .order('start_datetime', { ascending: true });

    if (error) throw error;

    // Transform the data to include creator_full_name
    events.value = data.map(event => ({
      ...event,
      creator_full_name: event.users?.full_name || null
    }));

    updateCalendarEvents();
  } catch (error) {
    console.error('Error fetching events:', error);
    toastMessage.value = '載入活動資料時發生錯誤';
  }
};

const loadApplications = async () => {
  try {
    const { data, error } = await supabase
      .from('event_applications')
      .select(`
        *,
        events (
          id,
          title,
          start_datetime,
          end_datetime,
          address,
          banner_photo,
          description
        )
      `)
      .eq('user_id', authStore.currentUser?.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    applications.value = data || [];
  } catch (error) {
    console.error('Error loading applications:', error);
  }
};

const loadHostedEvents = async () => {
  try {
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        event_applications(*)
      `)
      .eq('user_id', authStore.currentUser?.id)
      .order('start_datetime', { ascending: false });

    if (error) throw error;
    data.forEach((event: any) => {
      event.applicationCount = event.event_applications.filter((app: any) => ['confirmed', 'attended'].includes(app.status)).length;
      event.attendedCount = event.event_applications.filter((app: any) => app.status === 'attended').length;
    });
    hostedEvents.value = data || [];
  } catch (error) {
    console.error('Error loading hosted events:', error);
  }
};

const loadLikedEvents = async () => {
  try {
    // Load user's liked events
    const { data: likedData, error: likedError } = await supabase
      .from('user_liked_events')
      .select('event_id')
      .eq('user_id', authStore.currentUser?.id);

    if (likedError) throw likedError;

    if (likedData && likedData.length > 0) {
      // Get the event details for liked events
      const eventIds = likedData.map((item: { event_id: string }) => item.event_id);

      const { data: eventsData, error: eventsError } = await supabase
        .from('events')
        .select(`
          *,
          users:user_id (
            full_name
          )
        `)
        .in('id', eventIds)
        .order('start_datetime', { ascending: true });

      if (eventsError) throw eventsError;

      // Transform the data to include creator_full_name
      likedEvents.value = eventsData.map((event: any) => ({
        ...event,
        creator_full_name: event.users?.full_name || null
      }));
    } else {
      likedEvents.value = [];
    }
  } catch (error) {
    console.error('Error loading liked events:', error);
  }
};



const navigateToEvent = (event: Event) => {
  router.push(`/events/${event.id}`);
};

const toggleView = () => {
  isCalendarView.value = !isCalendarView.value;
};

const viewQRCode = (application: any) => {
  selectedApplication.value = application;
};

const viewEvent = (event: any) => {
  router.push(`/events/${event.id}`);
};

const editEvent = (event: any) => {
  // TODO: Implement event editing
  console.log('Edit event:', event);
};

const viewApplications = async (event: any) => {
  try {
    selectedEvent.value = event;

    const { data, error } = await supabase
      .from('event_applications')
      .select('*')
      .eq('event_id', event.id)
      .order('created_at', { ascending: true });

    if (error) throw error;
    eventApplications.value = data || [];
  } catch (error) {
    console.error('Error loading applications:', error);
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'pending':
      return '待確認';
    case 'confirmed':
      return '已確認';
    case 'cancelled':
      return '已取消';
    case 'attended':
      return '已出席';
    default:
      return status;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'confirmed':
      return 'success';
    case 'cancelled':
      return 'medium';
    case 'attended':
      return 'primary';
    default:
      return 'medium';
  }
};

const closeCreateEventModal = () => {
  showCreateEventModal.value = false;
  // Reset to prefilled data instead of empty values
  const tomorrowDate = new Date();
  tomorrowDate.setDate(tomorrowDate.getDate() + 1);
  const tomorrowStr = tomorrowDate.toISOString().split('T')[0];

  newEvent.value = {
    title: '',
    start_datetime: `${tomorrowStr}T14:00:00`,
    end_datetime: `${tomorrowStr}T17:00:00`,
    address: '',
    description: '',
    banner_photo: '',
    max_participants: 100
  };
  bannerPreview.value = null;
  bannerPhoto.value = null;
};

const takeBanner = async () => {
  try {
    const photo = await takePhoto();
    if (photo) {
      bannerPhoto.value = photo;
      bannerPreview.value = photo.base64Data || null;
    }
  } catch (error) {
    console.error('Error taking banner photo:', error);
    toastMessage.value = '無法獲取相片，請稍後再試';
  }
};

const removeBanner = () => {
  bannerPhoto.value = null;
  bannerPreview.value = null;
  newEvent.value.banner_photo = '';
};

const showCreateConfirmation = () => {
  showConfirmation.value = true;
};

const handleCreateEvent = async () => {
  if (!authStore.currentUser?.id) return;

  try {
    isSubmitting.value = true;

    // Upload banner photo if provided
    let bannerUrl = '';

    if (bannerPhoto.value && bannerPhoto.value.base64Data) {
      try {
        const imageInputs = [{
          base64Data: bannerPhoto.value.base64Data,
          filename: bannerPhoto.value.filepath || `banner_${new Date().getTime()}.jpg`,
          mimeType: bannerPhoto.value.mimeType || 'image/jpeg'
        }];

        const uploadedUrls = await uploadImages(imageInputs);
        if (uploadedUrls.length > 0) {
          bannerUrl = uploadedUrls[0];
        }
      } catch (error) {
        console.error('Error uploading banner photo:', error);
        toastMessage.value = '圖片上傳失敗，請稍後再試';
        throw error;
      }
    }

    // Use datetime values directly (already in HKT)
    const eventData: CreateEvent = {
      user_id: authStore.currentUser.id,
      title: newEvent.value.title!,
      start_datetime: newEvent.value.start_datetime!,
      end_datetime: newEvent.value.end_datetime!,
      address: newEvent.value.address!,
      description: newEvent.value.description!,
      banner_photo: bannerUrl,
      max_participants: newEvent.value.max_participants || null
    };

    const createdEvent = await api.createEvent(eventData);
    events.value = [createdEvent, ...events.value];
    updateCalendarEvents();

    toastMessage.value = '活動建立成功！';
    closeCreateEventModal();
  } catch (error: any) {
    console.error('Error creating event:', error);
    try {
      toastMessage.value = JSON.parse(error)[0].message;
    } catch (e) {
      toastMessage.value = error.message || '建立活動時發生錯誤';
    }
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
/* General layout */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.view-selector {
  padding: 0.5rem 1rem;
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Calendar view styles */
.calendar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.events-list {
  margin-top: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.date-header {
  padding: 1rem;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 1px solid var(--ion-color-light);
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.event-time, .event-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}

ion-thumbnail {
  width: 120px;
  height: 80px;
}

ion-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.no-events, .empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--ion-color-medium);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.no-events p, .empty-state p {
  margin: 0;
  font-size: 1.1rem;
}

/* List view styles */
.list-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Event card grid */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.event-card {
  margin: 0;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.event-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.event-image-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.event-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ion-color-light);
}

.event-date-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.event-date-day {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: var(--ion-color-primary);
}

.event-date-month {
  font-size: 0.8rem;
  text-transform: uppercase;
  color: var(--ion-color-medium);
}

.event-status-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

.past-event {
  opacity: 0.8;
}

.past-event .event-image {
  filter: grayscale(50%);
}

/* Event item styles */
.event-item {
  --padding-start: 1rem;
  --padding-end: 1rem;
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  --border-radius: 12px;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.event-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.event-actions ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  height: 36px;
}

.event-actions ion-icon {
  font-size: 1.2rem;
}

.attendance-info {
  color: var(--ion-color-medium);
  font-size: 0.9em;
  margin-top: 0.5rem;
}

/* QR code styles */
.qr-code-content {
  text-align: center;
  padding: 2rem;
}

.qr-code-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
}

.qr-code {
  margin: 2rem 0;
  display: inline-block;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code img {
  width: 200px;
  height: 200px;
}

.check-in-code {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed var(--ion-color-light-shade);
}

.check-in-code p {
  margin: 0;
  font-size: 1rem;
  color: var(--ion-color-dark);
}

.check-in-code span {
  font-weight: 700;
  font-size: 1.2rem;
  letter-spacing: 2px;
  color: var(--ion-color-primary);
}

.qr-note {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
  margin: 0;
}

/* Applications list styles */
.applications-list {
  padding: 1rem;
}

.applications-list h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
}

.applications-list p {
  color: var(--ion-color-medium);
  margin: 0 0 1.5rem;
}

.application-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.application-actions ion-button {
  --padding-start: 8px;
  --padding-end: 8px;
  height: 36px;
}

.application-actions ion-icon {
  font-size: 1.2rem;
}

/* Image upload styles */
.image-upload-container {
  width: 100%;
  padding: 1rem 0;
}

.preview-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.preview-container ion-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 8px;
  --padding-bottom: 8px;
  margin: 0;
}

.banner-preview {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid var(--ion-color-medium);
}

/* Calendar customization */
:deep(.sx__calendar) {
  --sx-color-primary: var(--ion-color-primary);
  --sx-color-primary-hover: var(--ion-color-primary-shade);
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  font-size: 16px !important;
  color: var(--ion-text-color);
}

:deep(.sx__calendar-header) {
  background: white;
  border-bottom: 1px solid var(--ion-color-light);
  padding: 1rem;
}

:deep(.sx__calendar-grid) {
  background: white;
  padding: 1rem;
}

:deep(.sx__calendar-date) {
  border-radius: 8px;
  font-size: 14px !important;
  height: 40px !important;
  color: var(--ion-text-color) !important;
}

:deep(.sx__calendar-date--today) {
  background: var(--ion-color-primary-tint);
  color: var(--ion-color-primary) !important;
}

:deep(.sx__calendar-date--selected) {
  background: var(--ion-color-primary);
  color: white !important;
}

:deep(.sx__calendar-event) {
  border-radius: 4px;
  padding: 2px 4px;
  color: white !important;
}

:deep(.sx__calendar-date--different-month) {
  color: var(--ion-color-medium) !important;
}

/* Responsive design */
@media (min-width: 768px) {
  .calendar-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .events-list {
    margin-top: 0;
    position: sticky;
    top: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

@media (max-width: 767px) {
  .events-grid {
    grid-template-columns: 1fr;
  }

  ion-thumbnail {
    width: 80px;
    height: 60px;
  }

  :deep(.sx__calendar) {
    font-size: 14px !important;
  }

  :deep(.sx__calendar-date) {
    font-size: 12px !important;
    height: 32px !important;
  }
}
</style>