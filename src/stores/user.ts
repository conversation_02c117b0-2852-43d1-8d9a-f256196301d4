import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User } from '@/services/schema';
import { UserSchema, } from '@/services/schema';
import { supabase } from '@/lib/supabase';

// Define a type for branch applications
interface BranchApplication {
  id: string;
  branch_id: string;
  user_id: string;
  status: string;
  message?: string;
  created_at: string;
}

export const useUserStore = defineStore('user', () => {
  const currentUser = ref<User | null>(null);
  const isAuthenticated = ref(false);
  const likedShopIds = ref<Set<string>>(new Set());
  const likedEventIds = ref<Set<string>>(new Set());
  const likedUserIds = ref<Set<string>>(new Set());

  // Computed properties for shop and branch
  const userShop = computed(() => currentUser.value?.shop || null);
  const userBranch = computed(() => currentUser.value?.branch || null);

  // Computed property for referrer (will be fetched separately if needed)
  const referrer = ref<any>(null);

  // Branch related (user-specific)
  const branchApplications = ref<BranchApplication[]>([]);
  const pendingBranchApplications = computed(() => {
    return branchApplications.value.filter(app => app.status === 'pending');
  });
  const branchMemberships = ref([]);

  const login = async (userData: unknown) => {
    try {
      // Validate raw data against schema
      UserSchema.parse(userData);
      currentUser.value = userData;
      isAuthenticated.value = true;

      // Process liked shops if available
      if (currentUser.value && 'userLikedShops' in currentUser.value) {
        const likedShops = (currentUser.value as any).userLikedShops || [];
        likedShopIds.value = new Set(likedShops.map((item: any) => item.shop_id));
      } else {
        likedShopIds.value = new Set();
      }

      // Process liked events if available
      if (currentUser.value && 'userLikedEvents' in currentUser.value) {
        const likedEvents = (currentUser.value as any).userLikedEvents || [];
        likedEventIds.value = new Set(likedEvents.map((item: any) => item.event_id));
      } else {
        likedEventIds.value = new Set();
      }

      // Process liked users if available
      if (currentUser.value && 'userLikedUsers' in currentUser.value) {
        const likedUsers = (currentUser.value as any).userLikedUsers || [];
        likedUserIds.value = new Set(likedUsers.map((item: any) => item.liked_user_id));
      } else {
        likedUserIds.value = new Set();
      }

      // Referrer
      referrer.value = currentUser.value?.referrer;

      // Branch related
      branchApplications.value = currentUser.value?.branchApplications || [];
      branchMemberships.value = currentUser.value?.branchMemberships || [];
    } catch (error) {
      console.error('Invalid user data:', error);
      logout();
      throw error;
    }
  };

  const logout = () => {
    currentUser.value = null;
    isAuthenticated.value = false;
    branchApplications.value = [];
    referrer.value = null;
    likedShopIds.value = new Set();
    likedEventIds.value = new Set();
    likedUserIds.value = new Set();
  };

  // Check if user has a pending application for a specific branch
  const hasPendingApplication = (branchId: string): boolean => {
    return branchApplications.value.some(
      app => app.branch_id === branchId && app.status === 'pending'
    );
  };

  // Add a new branch application
  const addBranchApplication = (application: BranchApplication) => {
    branchApplications.value.push(application);
  };

  // Check if a shop is liked by the current user
  const isShopLiked = (shopId: string): boolean => {
    return likedShopIds.value.has(shopId);
  };

  // Toggle favorite status for a shop
  const toggleFavorite = async (shopId: string): Promise<{ success: boolean; message: string }> => {
    if (!isAuthenticated.value || !currentUser.value?.id) {
      return { success: false, message: '請先登入以收藏商家' };
    }

    try {
      if (isShopLiked(shopId)) {
        // Remove from favorites in database
        const { error } = await supabase
          .from('user_liked_shops')
          .delete()
          .eq('user_id', currentUser.value.id)
          .eq('shop_id', shopId);

        if (error) throw error;

        // Update local state
        likedShopIds.value.delete(shopId);
        return { success: true, message: '已從收藏移除' };
      } else {
        // Add to favorites in database
        const { error } = await supabase
          .from('user_liked_shops')
          .insert({
            user_id: currentUser.value.id,
            shop_id: shopId
          });

        if (error) throw error;

        // Update local state
        likedShopIds.value.add(shopId);
        return { success: true, message: '已加入收藏' };
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      return { success: false, message: '操作失敗，請稍後再試' };
    }
  };

  // Check if an event is liked by the current user
  const isEventLiked = (eventId: string): boolean => {
    return likedEventIds.value.has(eventId);
  };

  // Toggle favorite status for an event
  const toggleEventFavorite = async (eventId: string): Promise<{ success: boolean; message: string }> => {
    if (!isAuthenticated.value || !currentUser.value?.id) {
      return { success: false, message: '請先登入以收藏活動' };
    }

    try {
      if (isEventLiked(eventId)) {
        // Remove from favorites in database
        const { error } = await supabase
          .from('user_liked_events')
          .delete()
          .eq('user_id', currentUser.value.id)
          .eq('event_id', eventId);

        if (error) throw error;

        // Update local state
        likedEventIds.value.delete(eventId);
        return { success: true, message: '已從收藏移除' };
      } else {
        // Add to favorites in database
        const { error } = await supabase
          .from('user_liked_events')
          .insert({
            user_id: currentUser.value.id,
            event_id: eventId
          });

        if (error) throw error;

        // Update local state
        likedEventIds.value.add(eventId);
        return { success: true, message: '已加入收藏' };
      }
    } catch (error) {
      console.error('Error toggling event favorite:', error);
      return { success: false, message: '操作失敗，請稍後再試' };
    }
  };

  // Check if a user is liked by the current user
  const isUserLiked = (userId: string): boolean => {
    return likedUserIds.value.has(userId);
  };

  // Toggle like status for a user
  const toggleUserLike = async (userId: string, notes?: string): Promise<{ success: boolean; message: string }> => {
    if (!isAuthenticated.value || !currentUser.value?.id) {
      return { success: false, message: '請先登入以收藏用戶' };
    }

    try {
      if (isUserLiked(userId)) {
        // Remove from liked users in database
        const { error } = await supabase
          .from('user_liked_users')
          .delete()
          .eq('user_id', currentUser.value.id)
          .eq('liked_user_id', userId);

        if (error) throw error;

        // Update local state
        likedUserIds.value.delete(userId);
        return { success: true, message: '已取消收藏用戶' };
      } else {
        // Add to liked users in database
        const { error } = await supabase
          .from('user_liked_users')
          .insert({
            user_id: currentUser.value.id,
            liked_user_id: userId,
            notes: notes || null
          });

        if (error) throw error;

        // Update local state
        likedUserIds.value.add(userId);
        return { success: true, message: '已收藏用戶' };
      }
    } catch (error) {
      console.error('Error toggling user like:', error);
      return { success: false, message: '操作失敗，請稍後再試' };
    }
  };

  // Update notes for a liked user
  const updateUserLikeNotes = async (userId: string, notes: string): Promise<{ success: boolean; message: string }> => {
    if (!isAuthenticated.value || !currentUser.value?.id) {
      return { success: false, message: '請先登入以更新備註' };
    }

    try {
      const { error } = await supabase
        .from('user_liked_users')
        .update({ notes, updated_at: new Date().toISOString() })
        .eq('user_id', currentUser.value.id)
        .eq('liked_user_id', userId);

      if (error) throw error;

      return { success: true, message: '備註已更新' };
    } catch (error) {
      console.error('Error updating user like notes:', error);
      return { success: false, message: '操作失敗，請稍後再試' };
    }
  };

  return {
    currentUser,
    isAuthenticated,
    branchApplications, pendingBranchApplications,
    branchMemberships,
    userShop,
    userBranch,
    referrer,
    likedShopIds,
    likedEventIds,
    likedUserIds,
    login,
    logout,
    hasPendingApplication,
    addBranchApplication,
    isShopLiked,
    toggleFavorite,
    isEventLiked,
    toggleEventFavorite,
    isUserLiked,
    toggleUserLike,
    updateUserLikeNotes
  };
});