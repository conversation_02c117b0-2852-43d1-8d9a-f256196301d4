import { CapacitorUpdater } from '@capgo/capacitor-updater';
import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';

export interface UpdateInfo {
  version: string;
  url: string;
  checksum?: string;
}

export class UpdateService {
  private static instance: UpdateService;
  private isChecking = false;
  private currentVersion = '';
  private lastCheckTime = 0;
  private readonly MIN_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes minimum between checks
  private readonly MAX_DAILY_CHECKS = 10; // Maximum checks per day
  private dailyCheckCount = 0;
  private lastCheckDate = '';

  private constructor() {}

  static getInstance(): UpdateService {
    if (!UpdateService.instance) {
      UpdateService.instance = new UpdateService();
    }
    return UpdateService.instance;
  }

  async initialize(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      console.log('UpdateService: Running on web, updates disabled');
      return;
    }

    try {
      // Get current app version
      const appInfo = await App.getInfo();
      this.currentVersion = appInfo.version;

      console.log('UpdateService: Initialized with version', this.currentVersion);

      // Set up listeners
      await this.setupListeners();

    } catch (error) {
      console.error('UpdateService: Failed to initialize', error);
    }
  }

  private async setupListeners(): Promise<void> {
    try {
      // Listen for download events
      await CapacitorUpdater.addListener('download', (info) => {
        console.log('UpdateService: Download progress', info);
      });

      // Listen for major available updates
      await CapacitorUpdater.addListener('majorAvailable', (info) => {
        console.log('UpdateService: Major update available', info);
        this.handleMajorUpdate(info);
      });

      // Listen for update available
      await CapacitorUpdater.addListener('updateAvailable', (info) => {
        console.log('UpdateService: Update available', info);
        this.handleUpdateAvailable(info);
      });

      // Listen for no update available
      await CapacitorUpdater.addListener('noNeedUpdate', (info) => {
        console.log('UpdateService: No update needed', info);
      });

      // Listen for update failed
      await CapacitorUpdater.addListener('updateFailed', (info) => {
        console.error('UpdateService: Update failed', info);
      });

      // Listen for download complete
      await CapacitorUpdater.addListener('downloadComplete', (info) => {
        console.log('UpdateService: Download complete', info);
        this.handleDownloadComplete(info);
      });

    } catch (error) {
      console.error('UpdateService: Failed to setup listeners', error);
    }
  }

  async checkForUpdates(): Promise<void> {
    if (!Capacitor.isNativePlatform() || this.isChecking) {
      return;
    }

    // Rate limiting to prevent infinite loops
    const now = Date.now();
    const today = new Date().toDateString();

    // Reset daily counter if it's a new day
    if (this.lastCheckDate !== today) {
      this.lastCheckDate = today;
      this.dailyCheckCount = 0;
    }

    // Check rate limits
    if (now - this.lastCheckTime < this.MIN_CHECK_INTERVAL) {
      console.log('UpdateService: Rate limited - too soon since last check');
      return;
    }

    if (this.dailyCheckCount >= this.MAX_DAILY_CHECKS) {
      console.log('UpdateService: Rate limited - max daily checks reached');
      return;
    }

    this.isChecking = true;
    this.lastCheckTime = now;
    this.dailyCheckCount++;

    try {
      console.log(`UpdateService: Checking for updates... (${this.dailyCheckCount}/${this.MAX_DAILY_CHECKS} today)`);

      // Check for updates from GitHub releases
      const updateInfo = await this.fetchLatestRelease();

      if (updateInfo && this.isNewerVersion(updateInfo.version)) {
        console.log('UpdateService: New version available:', updateInfo.version);
        await this.downloadAndInstallUpdate(updateInfo);
      } else {
        console.log('UpdateService: App is up to date');
      }

    } catch (error) {
      console.error('UpdateService: Error checking for updates', error);
    } finally {
      this.isChecking = false;
    }
  }

  private async fetchLatestRelease(): Promise<UpdateInfo | null> {
    try {
      const response = await fetch('https://api.github.com/repos/mlolpet/syner-biz-releases/releases/latest');

      if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status}`);
      }

      const release = await response.json();

      // Look for the update bundle asset
      const updateAsset = release.assets.find((asset: any) =>
        asset.name.endsWith('.zip') && asset.name.includes('update')
      );

      if (!updateAsset) {
        console.log('UpdateService: No update bundle found in latest release');
        return null;
      }

      return {
        version: release.tag_name.replace('v', ''), // Remove 'v' prefix if present
        url: updateAsset.browser_download_url,
        checksum: updateAsset.checksum || undefined
      };

    } catch (error) {
      console.error('UpdateService: Failed to fetch latest release', error);
      return null;
    }
  }

  private isNewerVersion(remoteVersion: string): boolean {
    try {
      // Check if we've already processed this version
      const lastProcessedVersion = localStorage.getItem('lastProcessedUpdateVersion');
      if (lastProcessedVersion === remoteVersion) {
        console.log('UpdateService: Version already processed:', remoteVersion);
        return false;
      }

      // Simple version comparison (assumes semantic versioning)
      const current = this.currentVersion.split('.').map(Number);
      const remote = remoteVersion.split('.').map(Number);

      for (let i = 0; i < Math.max(current.length, remote.length); i++) {
        const currentPart = current[i] || 0;
        const remotePart = remote[i] || 0;

        if (remotePart > currentPart) {
          // Mark this version as processed to prevent re-downloading
          localStorage.setItem('lastProcessedUpdateVersion', remoteVersion);
          return true;
        }
        if (remotePart < currentPart) return false;
      }

      return false;
    } catch (error) {
      console.error('UpdateService: Error comparing versions', error);
      return false;
    }
  }

  private async downloadAndInstallUpdate(updateInfo: UpdateInfo): Promise<void> {
    try {
      console.log('UpdateService: Downloading update from', updateInfo.url);

      // Download the update
      const downloadResult = await CapacitorUpdater.download({
        url: updateInfo.url,
        version: updateInfo.version,
        sessionKey: '',
        checksum: updateInfo.checksum
      });

      console.log('UpdateService: Download result', downloadResult);

      if (downloadResult) {
        // Set the new bundle
        await CapacitorUpdater.set(downloadResult);
        console.log('UpdateService: Update installed, will reload on next app start');
      }

    } catch (error) {
      console.error('UpdateService: Failed to download/install update', error);
    }
  }

  private async handleMajorUpdate(info: any): Promise<void> {
    console.log('UpdateService: Major update detected, user should update from store', info);
    // For major updates, you might want to show a dialog directing users to the app store
  }

  private async handleUpdateAvailable(info: any): Promise<void> {
    console.log('UpdateService: Update available, starting download', info);
  }

  private async handleDownloadComplete(info: any): Promise<void> {
    console.log('UpdateService: Download complete, setting bundle', info);
    try {
      await CapacitorUpdater.set(info.bundle);
    } catch (error) {
      console.error('UpdateService: Failed to set bundle', error);
    }
  }

  async getCurrentVersion(): Promise<string> {
    try {
      const appInfo = await App.getInfo();
      return appInfo.version;
    } catch (error) {
      console.error('UpdateService: Failed to get current version', error);
      return '0.0.0';
    }
  }

  async forceReload(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      window.location.reload();
      return;
    }

    try {
      await CapacitorUpdater.reload();
    } catch (error) {
      console.error('UpdateService: Failed to reload', error);
    }
  }
}

// Export singleton instance
export const updateService = UpdateService.getInstance();
